<libraries>
  <library
      name="androidx.compose.material3:material3-android:1.3.2@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\cfbe6762a9ebca8f43d22b008e11bcd0\transformed\material3-release\jars\classes.jar"
      resolved="androidx.compose.material3:material3-android:1.3.2"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\cfbe6762a9ebca8f43d22b008e11bcd0\transformed\material3-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.8.2@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\4c4454d6cf1a0487e1e6c8a26bd343ad\transformed\ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.8.2"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\4c4454d6cf1a0487e1e6c8a26bd343ad\transformed\ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.8.2@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\a805b3df82c0f1d10d3488a563f12d73\transformed\ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.8.2"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\a805b3df82c0f1d10d3488a563f12d73\transformed\ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.8.2@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\81c1444e3af2b7d8ecac2357f5832d6b\transformed\ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.8.2"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\81c1444e3af2b7d8ecac2357f5832d6b\transformed\ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.8.2@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\753fd0e6095def29bc665c94637b31cd\transformed\foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.8.2"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\753fd0e6095def29bc665c94637b31cd\transformed\foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple-android:1.8.2@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\328507aa9e591fb7b26767f099dcfb6e\transformed\material-ripple-release\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple-android:1.8.2"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\328507aa9e591fb7b26767f099dcfb6e\transformed\material-ripple-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.8.2@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\5b4ba3bb7e234dc8495fb2bbdf42cb63\transformed\foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.8.2"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\5b4ba3bb7e234dc8495fb2bbdf42cb63\transformed\foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.8.2@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\1434ae527be5227b50dea6cff65aeecd\transformed\animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.8.2"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\1434ae527be5227b50dea6cff65aeecd\transformed\animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.8.2@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\936ae82b5d3e3566df7ca9825d10d95d\transformed\animation-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.8.2"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\936ae82b5d3e3566df7ca9825d10d95d\transformed\animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.8.2@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\c47dbf3d10b6776e22b3f43f2fc410c6\transformed\ui-geometry-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.8.2"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\c47dbf3d10b6776e22b3f43f2fc410c6\transformed\ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-data-android:1.8.2@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\a24328e76c4ea592d2d3af0c8a2b9c6e\transformed\ui-tooling-data-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-data-android:1.8.2"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\a24328e76c4ea592d2d3af0c8a2b9c6e\transformed\ui-tooling-data-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.8.2@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\f1e68065b061fd3f9688025e9d310d54\transformed\ui-tooling-preview-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.8.2"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\f1e68065b061fd3f9688025e9d310d54\transformed\ui-tooling-preview-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.8.2@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\367453924602fce52a1da29b574a3bf6\transformed\ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.8.2"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\367453924602fce52a1da29b574a3bf6\transformed\ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.10.1@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\f1e5017d927147ee57550e382728a21c\transformed\activity-ktx-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.10.1"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\f1e5017d927147ee57550e382728a21c\transformed\activity-ktx-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.16.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\jars\classes.jar"
      resolved="androidx.core:core:1.16.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\a58900e3e5094897d10ae5fd311db10e\transformed\lifecycle-livedata-core-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.9.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\a58900e3e5094897d10ae5fd311db10e\transformed\lifecycle-livedata-core-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\ce0befb1356a7a9ef092407c84b56366\transformed\lifecycle-runtime-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\ce0befb1356a7a9ef092407c84b56366\transformed\lifecycle-runtime-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\9a9ea0066305f6c8846b99ed4c813481\transformed\lifecycle-viewmodel-savedstate-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\9a9ea0066305f6c8846b99ed4c813481\transformed\lifecycle-viewmodel-savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\ab3d1566985d0eb315118d7648207d2e\transformed\lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.9.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\ab3d1566985d0eb315118d7648207d2e\transformed\lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\a8b41728e4b0de1ded866e01dadfdf54\transformed\savedstate-ktx-1.3.0\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.3.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\a8b41728e4b0de1ded866e01dadfdf54\transformed\savedstate-ktx-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-android:1.3.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\db34985b86d559424c1a8bc12904a68c\transformed\savedstate-release\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-android:1.3.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\db34985b86d559424c1a8bc12904a68c\transformed\savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.9.0\ab739bccdb3541983385af169565c7c035d897e7\lifecycle-common-jvm-2.9.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.9.0"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\1a91f0e1379b47d50c6190e7bb707fab\transformed\lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\1a91f0e1379b47d50c6190e7bb707fab\transformed\lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\a6dfb9cc4481f505e665b064184db016\transformed\lifecycle-viewmodel-ktx-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\a6dfb9cc4481f505e665b064184db016\transformed\lifecycle-viewmodel-ktx-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\f79b6a98d14b9cbcfa7708c8935b6592\transformed\lifecycle-runtime-ktx\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\f79b6a98d14b9cbcfa7708c8935b6592\transformed\lifecycle-runtime-ktx"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\e5ac02f54f3d2dd10311886e9484c473\transformed\lifecycle-viewmodel-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\e5ac02f54f3d2dd10311886e9484c473\transformed\lifecycle-viewmodel-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core-android:1.7.8@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\6067fae33e9a7edfaf370d73067214e1\transformed\material-icons-core-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core-android:1.7.8"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\6067fae33e9a7edfaf370d73067214e1\transformed\material-icons-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.8.2@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\f1146f774d6258b00f26aa6ad8af6b00\transformed\ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.8.2"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\f1146f774d6258b00f26aa6ad8af6b00\transformed\ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-android:1.8.2@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\d3cf6caf4f6734eb2de3b4ee5e489228\transformed\ui-tooling-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-android:1.8.2"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\d3cf6caf4f6734eb2de3b4ee5e489228\transformed\ui-tooling-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-test-manifest:1.8.2@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\4dd3fdaaff8835a6d69528f823645a30\transformed\ui-test-manifest-1.8.2\jars\classes.jar"
      resolved="androidx.compose.ui:ui-test-manifest:1.8.2"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\4dd3fdaaff8835a6d69528f823645a30\transformed\ui-test-manifest-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.10.1@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\2a7f45898462c893c925156d163f656f\transformed\activity-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity:1.10.1"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\2a7f45898462c893c925156d163f656f\transformed\activity-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.10.1@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\d2f3baf9da570c6bc7841b93e17ab012\transformed\activity-compose-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity-compose:1.10.1"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\d2f3baf9da570c6bc7841b93e17ab012\transformed\activity-compose-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.16.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\7af4b4f491a6596e64d50ced738265c5\transformed\core-ktx-1.16.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.16.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\7af4b4f491a6596e64d50ced738265c5\transformed\core-ktx-1.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-datasource-okhttp:1.4.1@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\f1ca47f26625df15409925c9a2b47abe\transformed\media3-datasource-okhttp-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-datasource-okhttp:1.4.1"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\f1ca47f26625df15409925c9a2b47abe\transformed\media3-datasource-okhttp-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.retrofit2:converter-gson:2.11.0@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-gson\2.11.0\9d1fe9d1662de0548e08e293041140a8e4026f81\converter-gson-2.11.0.jar"
      resolved="com.squareup.retrofit2:converter-gson:2.11.0"/>
  <library
      name="com.squareup.retrofit2:retrofit:2.11.0@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\2.11.0\6ca8c6caf842271f3232e075519fe04081ef7069\retrofit-2.11.0.jar"
      resolved="com.squareup.retrofit2:retrofit:2.11.0"/>
  <library
      name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\logging-interceptor\4.12.0\e922c1f14d365c0f2bed140cc0825e18462c2778\logging-interceptor-4.12.0.jar"
      resolved="com.squareup.okhttp3:logging-interceptor:4.12.0"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.8.2@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\97500f0365f817c48aeef75c3624e1ff\transformed\runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.8.2"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\97500f0365f817c48aeef75c3624e1ff\transformed\runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.8.2@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\9aeaa20feb746deed08812d1648685c4\transformed\runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.8.2"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\9aeaa20feb746deed08812d1648685c4\transformed\runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="com.squareup.okio:okio-jvm:3.6.0@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.6.0\5600569133b7bdefe1daf9ec7f4abeb6d13e1786\okio-jvm-3.6.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.6.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.10\c7510d64a83411a649c76f2778304ddf71d7437b\kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"/>
  <library
      name="androidx.core:core-viewtree:1.0.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\edea32bee283cb823eb7cb435e4a8ee9\transformed\core-viewtree-1.0.0\jars\classes.jar"
      resolved="androidx.core:core-viewtree:1.0.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\edea32bee283cb823eb7cb435e4a8ee9\transformed\core-viewtree-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\98ab0d71b60d4f922b7af62eb452e747\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\98ab0d71b60d4f922b7af62eb452e747\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-jvm:1.5.0@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.5.0\7ba2c69414d46ebc2dd76598bdd0a75c54281a57\collection-jvm-1.5.0.jar"
      resolved="androidx.collection:collection-jvm:1.5.0"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.9.1\b17951747e38bf3986a24431b9ba0d039958aa5f\annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="androidx.media3:media3-exoplayer:1.4.1@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\457147f8f67deaf2a3ec4b5417c706c3\transformed\media3-exoplayer-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer:1.4.1"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\457147f8f67deaf2a3ec4b5417c706c3\transformed\media3-exoplayer-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-container:1.4.1@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\c4d2642a1e1ee99b380a5d6c12943d6e\transformed\media3-container-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-container:1.4.1"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\c4d2642a1e1ee99b380a5d6c12943d6e\transformed\media3-container-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-common:1.4.1@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\b7060434c7d28a33d9a9f290017a7cfc\transformed\media3-common-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-common:1.4.1"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\b7060434c7d28a33d9a9f290017a7cfc\transformed\media3-common-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\f26d33392aad83b4a93bce8ea0c88368\transformed\annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\f26d33392aad83b4a93bce8ea0c88368\transformed\annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.10\bc5bfc2690338defd5195b05c57562f2194eeb10\kotlin-stdlib-jdk7-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-core-jvm\1.7.3\1f226780b845ff9206474c05159245d861556249\kotlinx-serialization-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.21\618b539767b4899b4660a83006e052b63f1db551\kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"/>
  <library
      name="androidx.media3:media3-ui:1.4.1@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\e874b47f582ea1f96bd82c21770ae83f\transformed\media3-ui-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-ui:1.4.1"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\e874b47f582ea1f96bd82c21770ae83f\transformed\media3-ui-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-database:1.4.1@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\276f849608b7c02181eb5c8c0c190176\transformed\media3-database-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-database:1.4.1"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\276f849608b7c02181eb5c8c0c190176\transformed\media3-database-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.code.gson:gson:2.11.0@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.11.0\527175ca6d81050b53bdd4c457a6d6e017626b0e\gson-2.11.0.jar"
      resolved="com.google.code.gson:gson:2.11.0"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.guava:guava:33.0.0-android@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\33.0.0-android\cfbbdc54f232feedb85746aeeea0722f5244bb9a\guava-33.0.0-android.jar"
      resolved="com.google.guava:guava:33.0.0-android"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="org.jspecify:jspecify:1.0.0@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\org.jspecify\jspecify\1.0.0\7425a601c1c7ec76645a78d22b8c6a627edee507\jspecify-1.0.0.jar"
      resolved="org.jspecify:jspecify:1.0.0"/>
  <library
      name="com.google.guava:failureaccess:1.0.2@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.2\c4a06a64e650562f30b7bf9aaec1bfed43aca12b\failureaccess-1.0.2.jar"
      resolved="com.google.guava:failureaccess:1.0.2"/>
  <library
      name="androidx.media3:media3-datasource:1.4.1@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\6394a968d6a4132989a7b0c638a1faee\transformed\media3-datasource-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-datasource:1.4.1"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\6394a968d6a4132989a7b0c638a1faee\transformed\media3-datasource-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-decoder:1.4.1@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\06241f7593279a67eb2ced5b48ecfa12\transformed\media3-decoder-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-decoder:1.4.1"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\06241f7593279a67eb2ced5b48ecfa12\transformed\media3-decoder-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-extractor:1.4.1@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\09cc9cf0612adf3b40c75a9ca6a4a2c4\transformed\media3-extractor-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-extractor:1.4.1"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\09cc9cf0612adf3b40c75a9ca6a4a2c4\transformed\media3-extractor-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.27.0@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.27.0\91b2c29d8a6148b5e2e4930f070d4840e2e48e34\error_prone_annotations-2.27.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.27.0"/>
  <library
      name="androidx.media:media:1.7.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\a93d3d80b5c237957b54b09cb8789763\transformed\media-1.7.0\jars\classes.jar"
      resolved="androidx.media:media:1.7.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\a93d3d80b5c237957b54b09cb8789763\transformed\media-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.3.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\ad908614da85acc9911c8dc4731a8333\transformed\recyclerview-1.3.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.3.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\ad908614da85acc9911c8dc4731a8333\transformed\recyclerview-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-android:1.8.2@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\8a2e8e7142c38a8f2ee7133e4de5b14c\transformed\material-release\jars\classes.jar"
      resolved="androidx.compose.material:material-android:1.8.2"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\8a2e8e7142c38a8f2ee7133e4de5b14c\transformed\material-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\c92be2f7345f54bffbd3fef688cfc3ea\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\c92be2f7345f54bffbd3fef688cfc3ea\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.0.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\7b5628fa636fa2f883564c96afca4908\transformed\autofill-1.0.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.0.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\7b5628fa636fa2f883564c96afca4908\transformed\autofill-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.graphics:graphics-path:1.0.1@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\00573c5762cd403b25d6a3b98063a7ae\transformed\graphics-path-1.0.1\jars\classes.jar"
      resolved="androidx.graphics:graphics-path:1.0.1"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\00573c5762cd403b25d6a3b98063a7ae\transformed\graphics-path-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.4.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\jars\classes.jar;D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.4.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.9.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\92a51bc7c02ff55fe83543917b2f6d99\transformed\lifecycle-process-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.9.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\92a51bc7c02ff55fe83543917b2f6d99\transformed\lifecycle-process-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.9.0@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.9.0\86a8e6b9d0ba0c964fdc7223a38b6ce74bcb24dd\lifecycle-common-java8-2.9.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.9.0"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\d600dd0990b6dcf9425752e5cf74f8fb\transformed\customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\d600dd0990b6dcf9425752e5cf74f8fb\transformed\customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.5.0@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.5.0\27c78a926a16a1bf792b2285cf2834e8caae4a07\collection-ktx-1.5.0.jar"
      resolved="androidx.collection:collection-ktx:1.5.0"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.6@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\5858ef100c4cfcd4b2676b4bc0b4d5ed\transformed\exifinterface-1.3.6\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.6"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\5858ef100c4cfcd4b2676b4bc0b4d5ed\transformed\exifinterface-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\6ec472f0f3951334fe806e2ee6a2ef6d\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\6ec472f0f3951334fe806e2ee6a2ef6d\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.4.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\418d17911481cf6fb34ad14d796f81b1\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\418d17911481cf6fb34ad14d796f81b1\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\975c1d8adc23a6c6f997efa5b6192589\transformed\tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\975c1d8adc23a6c6f997efa5b6192589\transformed\tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="D:\SDK\.gradle\caches\8.11.1\transforms\58b6d2d79e584f1872a1217950ffd323\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="D:\SDK\.gradle\caches\8.11.1\transforms\58b6d2d79e584f1872a1217950ffd323\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="D:\SDK\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
</libraries>
