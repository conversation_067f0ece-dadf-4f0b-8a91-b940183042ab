{"logs": [{"outputFile": "com.example.tik555.app-mergeDebugResources-2:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,296,419,539,641,740,856,997,1115,1260,1344,1446,1544,1644,1759,1886,1993,2138,2282,2428,2620,2758,2879,3003,3129,3228,3325,3450,3588,3692,3805,3910,4056,4207,4317,4422,4508,4603,4698,4812,4902,4989,5090,5170,5254,5355,5460,5553,5653,5741,5851,5952,6057,6176,6256,6360", "endColumns": "120,119,122,119,101,98,115,140,117,144,83,101,97,99,114,126,106,144,143,145,191,137,120,123,125,98,96,124,137,103,112,104,145,150,109,104,85,94,94,113,89,86,100,79,83,100,104,92,99,87,109,100,104,118,79,103,95", "endOffsets": "171,291,414,534,636,735,851,992,1110,1255,1339,1441,1539,1639,1754,1881,1988,2133,2277,2423,2615,2753,2874,2998,3124,3223,3320,3445,3583,3687,3800,3905,4051,4202,4312,4417,4503,4598,4693,4807,4897,4984,5085,5165,5249,5350,5455,5548,5648,5736,5846,5947,6052,6171,6251,6355,6451"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5909,6030,6150,6273,6393,6495,6594,6710,6851,6969,7114,7198,7300,7398,7498,7613,7740,7847,7992,8136,8282,8474,8612,8733,8857,8983,9082,9179,9304,9442,9546,9659,9764,9910,10061,10171,10276,10362,10457,10552,10666,10756,10843,10944,11024,11108,11209,11314,11407,11507,11595,11705,11806,11911,12030,12110,12214", "endColumns": "120,119,122,119,101,98,115,140,117,144,83,101,97,99,114,126,106,144,143,145,191,137,120,123,125,98,96,124,137,103,112,104,145,150,109,104,85,94,94,113,89,86,100,79,83,100,104,92,99,87,109,100,104,118,79,103,95", "endOffsets": "6025,6145,6268,6388,6490,6589,6705,6846,6964,7109,7193,7295,7393,7493,7608,7735,7842,7987,8131,8277,8469,8607,8728,8852,8978,9077,9174,9299,9437,9541,9654,9759,9905,10056,10166,10271,10357,10452,10547,10661,10751,10838,10939,11019,11103,11204,11309,11402,11502,11590,11700,11801,11906,12025,12105,12209,12305"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\8a2e8e7142c38a8f2ee7133e4de5b14c\\transformed\\material-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "141", "startColumns": "4", "startOffsets": "12310", "endColumns": "88", "endOffsets": "12394"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "198,293,376,473,572,658,737,834,925,1012,1097,1187,1263,1348,1424,1503,1578,1654,1726", "endColumns": "94,82,96,98,85,78,96,90,86,84,89,75,84,75,78,74,75,71,121", "endOffsets": "288,371,468,567,653,732,829,920,1007,1092,1182,1258,1343,1419,1498,1573,1649,1721,1843"}, "to": {"startLines": "27,28,29,30,31,82,83,142,143,144,145,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1532,1627,1710,1807,1906,5733,5812,12399,12490,12577,12662,12752,12828,12913,12989,13068,13244,13320,13392", "endColumns": "94,82,96,98,85,78,96,90,86,84,89,75,84,75,78,74,75,71,121", "endOffsets": "1622,1705,1802,1901,1987,5807,5904,12485,12572,12657,12747,12823,12908,12984,13063,13138,13315,13387,13509"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,147,234", "endColumns": "91,86,88", "endOffsets": "142,229,318"}, "to": {"startLines": "19,155,156", "startColumns": "4,4,4", "startOffsets": "708,13514,13601", "endColumns": "91,86,88", "endOffsets": "795,13596,13685"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "20,21,22,23,24,25,26,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "800,897,999,1098,1198,1305,1411,13143", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "892,994,1093,1193,1300,1406,1527,13239"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\17d8797a605bf267136b984c4f4c6900\\transformed\\media3-ui-1.7.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,658,739,822,895,989,1079,1153,1220,1317,1414,1480,1549,1616,1687,1798,1909,2019,2086,2172,2245,2319,2406,2495,2559,2626,2679,2737,2785,2846,2911,2979,3044,3113,3177,3238,3304,3357,3417,3491,3565,3624", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,80,82,72,93,89,73,66,96,96,65,68,66,70,110,110,109,66,85,72,73,86,88,63,66,52,57,47,60,64,67,64,68,63,60,65,52,59,73,73,58,70", "endOffsets": "280,466,653,734,817,890,984,1074,1148,1215,1312,1409,1475,1544,1611,1682,1793,1904,2014,2081,2167,2240,2314,2401,2490,2554,2621,2674,2732,2780,2841,2906,2974,3039,3108,3172,3233,3299,3352,3412,3486,3560,3619,3690"}, "to": {"startLines": "2,11,15,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,1992,2073,2156,2229,2323,2413,2487,2554,2651,2748,2814,2883,2950,3021,3132,3243,3353,3420,3506,3579,3653,3740,3829,3893,4664,4717,4775,4823,4884,4949,5017,5082,5151,5215,5276,5342,5395,5455,5529,5603,5662", "endLines": "10,14,18,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,80,82,72,93,89,73,66,96,96,65,68,66,70,110,110,109,66,85,72,73,86,88,63,66,52,57,47,60,64,67,64,68,63,60,65,52,59,73,73,58,70", "endOffsets": "330,516,703,2068,2151,2224,2318,2408,2482,2549,2646,2743,2809,2878,2945,3016,3127,3238,3348,3415,3501,3574,3648,3735,3824,3888,3955,4712,4770,4818,4879,4944,5012,5077,5146,5210,5271,5337,5390,5450,5524,5598,5657,5728"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\dc0228f35d5d9920c8fe531ae3e0365a\\transformed\\media3-exoplayer-1.7.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,194,261,332,414,496,591,680", "endColumns": "74,63,66,70,81,81,94,88,78", "endOffsets": "125,189,256,327,409,491,586,675,754"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3960,4035,4099,4166,4237,4319,4401,4496,4585", "endColumns": "74,63,66,70,81,81,94,88,78", "endOffsets": "4030,4094,4161,4232,4314,4396,4491,4580,4659"}}]}]}