{"logs": [{"outputFile": "com.example.tik555.app-mergeDebugResources-2:/values-sr/values-sr.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\8a2e8e7142c38a8f2ee7133e4de5b14c\\transformed\\material-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "143", "startColumns": "4", "startOffsets": "12272", "endColumns": "92", "endOffsets": "12360"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,287,414,530,628,722,833,969,1088,1230,1315,1415,1510,1608,1724,1849,1954,2095,2235,2368,2548,2673,2793,2918,3040,3136,3234,3351,3481,3581,3683,3792,3934,4083,4192,4295,4372,4470,4568,4677,4766,4852,4959,5039,5122,5219,5322,5415,5513,5600,5708,5805,5907,6040,6120,6227", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "166,282,409,525,623,717,828,964,1083,1225,1310,1410,1505,1603,1719,1844,1949,2090,2230,2363,2543,2668,2788,2913,3035,3131,3229,3346,3476,3576,3678,3787,3929,4078,4187,4290,4367,4465,4563,4672,4761,4847,4954,5034,5117,5214,5317,5410,5508,5595,5703,5800,5902,6035,6115,6222,6319"}, "to": {"startLines": "86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6003,6119,6235,6362,6478,6576,6670,6781,6917,7036,7178,7263,7363,7458,7556,7672,7797,7902,8043,8183,8316,8496,8621,8741,8866,8988,9084,9182,9299,9429,9529,9631,9740,9882,10031,10140,10243,10320,10418,10516,10625,10714,10800,10907,10987,11070,11167,11270,11363,11461,11548,11656,11753,11855,11988,12068,12175", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "6114,6230,6357,6473,6571,6665,6776,6912,7031,7173,7258,7358,7453,7551,7667,7792,7897,8038,8178,8311,8491,8616,8736,8861,8983,9079,9177,9294,9424,9524,9626,9735,9877,10026,10135,10238,10315,10413,10511,10620,10709,10795,10902,10982,11065,11162,11265,11358,11456,11543,11651,11748,11850,11983,12063,12170,12267"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,231", "endColumns": "87,87,89", "endOffsets": "138,226,316"}, "to": {"startLines": "21,157,158", "startColumns": "4,4,4", "startOffsets": "867,13468,13556", "endColumns": "87,87,89", "endOffsets": "950,13551,13641"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "22,23,24,25,26,27,28,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "955,1053,1155,1252,1356,1460,1565,13100", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "1048,1150,1247,1351,1455,1560,1676,13196"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "194,291,378,475,576,662,739,830,922,1007,1087,1172,1245,1334,1411,1489,1565,1644,1714", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,88,76,77,75,78,69,117", "endOffsets": "286,373,470,571,657,734,825,917,1002,1082,1167,1240,1329,1406,1484,1560,1639,1709,1827"}, "to": {"startLines": "29,30,31,32,33,84,85,144,145,146,147,148,149,150,151,152,154,155,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1681,1778,1865,1962,2063,5835,5912,12365,12457,12542,12622,12707,12780,12869,12946,13024,13201,13280,13350", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,88,76,77,75,78,69,117", "endOffsets": "1773,1860,1957,2058,2144,5907,5998,12452,12537,12617,12702,12775,12864,12941,13019,13095,13275,13345,13463"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\17d8797a605bf267136b984c4f4c6900\\transformed\\media3-ui-1.7.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,556,817,898,980,1062,1151,1242,1312,1378,1471,1565,1633,1697,1760,1832,1939,2049,2158,2234,2321,2394,2465,2556,2648,2715,2780,2833,2891,2939,3000,3066,3130,3193,3258,3322,3383,3449,3501,3563,3639,3715,3771", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,80,81,81,88,90,69,65,92,93,67,63,62,71,106,109,108,75,86,72,70,90,91,66,64,52,57,47,60,65,63,62,64,63,60,65,51,61,75,75,55,67", "endOffsets": "282,551,812,893,975,1057,1146,1237,1307,1373,1466,1560,1628,1692,1755,1827,1934,2044,2153,2229,2316,2389,2460,2551,2643,2710,2775,2828,2886,2934,2995,3061,3125,3188,3253,3317,3378,3444,3496,3558,3634,3710,3766,3834"}, "to": {"startLines": "2,11,16,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,606,2149,2230,2312,2394,2483,2574,2644,2710,2803,2897,2965,3029,3092,3164,3271,3381,3490,3566,3653,3726,3797,3888,3980,4047,4776,4829,4887,4935,4996,5062,5126,5189,5254,5318,5379,5445,5497,5559,5635,5711,5767", "endLines": "10,15,20,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83", "endColumns": "17,12,12,80,81,81,88,90,69,65,92,93,67,63,62,71,106,109,108,75,86,72,70,90,91,66,64,52,57,47,60,65,63,62,64,63,60,65,51,61,75,75,55,67", "endOffsets": "332,601,862,2225,2307,2389,2478,2569,2639,2705,2798,2892,2960,3024,3087,3159,3266,3376,3485,3561,3648,3721,3792,3883,3975,4042,4107,4824,4882,4930,4991,5057,5121,5184,5249,5313,5374,5440,5492,5554,5630,5706,5762,5830"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\dc0228f35d5d9920c8fe531ae3e0365a\\transformed\\media3-exoplayer-1.7.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,190,255,326,404,476,563,646", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "124,185,250,321,399,471,558,641,714"}, "to": {"startLines": "58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4112,4186,4247,4312,4383,4461,4533,4620,4703", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "4181,4242,4307,4378,4456,4528,4615,4698,4771"}}]}]}