package com.example.tik555

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.example.tik555.ui.screens.VideoScreen
import com.example.tik555.ui.theme.Tik555Theme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 设置全屏模式
        WindowCompat.setDecorFitsSystemWindows(window, false)

        setContent {
            Tik555Theme {
                // 设置状态栏样式
                SetupStatusBar()

                VideoScreen(
                    modifier = Modifier.fillMaxSize()
                )
            }
        }
    }
}

@Composable
private fun SetupStatusBar() {
    val view = LocalView.current

    SideEffect {
        val window = (view.context as ComponentActivity).window
        val windowInsetsController = WindowCompat.getInsetsController(window, view)

        // 设置状态栏透明
        @Suppress("DEPRECATION")
        window.statusBarColor = android.graphics.Color.TRANSPARENT

        // 设置状态栏文字为白色
        windowInsetsController.isAppearanceLightStatusBars = false

        // 隐藏导航栏
        windowInsetsController.hide(WindowInsetsCompat.Type.navigationBars())
        windowInsetsController.systemBarsBehavior =
            WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
    }
}