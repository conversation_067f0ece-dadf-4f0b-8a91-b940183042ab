{"logs": [{"outputFile": "com.example.tik555.app-mergeReleaseResources-2:/values-my/values-my.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,243", "endColumns": "78,108,107", "endOffsets": "129,238,346"}, "to": {"startLines": "19,154,155", "startColumns": "4,4,4", "startOffsets": "737,13500,13609", "endColumns": "78,108,107", "endOffsets": "811,13604,13712"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,192,326,437,571,686,786,903,1052,1176,1339,1425,1524,1617,1719,1839,1966,2070,2196,2327,2471,2639,2761,2878,2997,3124,3218,3315,3446,3583,3685,3797,3902,4028,4157,4260,4363,4444,4542,4638,4746,4833,4919,5038,5118,5202,5302,5404,5500,5598,5685,5792,5891,5992,6113,6193,6316", "endColumns": "136,133,110,133,114,99,116,148,123,162,85,98,92,101,119,126,103,125,130,143,167,121,116,118,126,93,96,130,136,101,111,104,125,128,102,102,80,97,95,107,86,85,118,79,83,99,101,95,97,86,106,98,100,120,79,122,117", "endOffsets": "187,321,432,566,681,781,898,1047,1171,1334,1420,1519,1612,1714,1834,1961,2065,2191,2322,2466,2634,2756,2873,2992,3119,3213,3310,3441,3578,3680,3792,3897,4023,4152,4255,4358,4439,4537,4633,4741,4828,4914,5033,5113,5197,5297,5399,5495,5593,5680,5787,5886,5987,6108,6188,6311,6429"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6016,6153,6287,6398,6532,6647,6747,6864,7013,7137,7300,7386,7485,7578,7680,7800,7927,8031,8157,8288,8432,8600,8722,8839,8958,9085,9179,9276,9407,9544,9646,9758,9863,9989,10118,10221,10324,10405,10503,10599,10707,10794,10880,10999,11079,11163,11263,11365,11461,11559,11646,11753,11852,11953,12074,12154,12277", "endColumns": "136,133,110,133,114,99,116,148,123,162,85,98,92,101,119,126,103,125,130,143,167,121,116,118,126,93,96,130,136,101,111,104,125,128,102,102,80,97,95,107,86,85,118,79,83,99,101,95,97,86,106,98,100,120,79,122,117", "endOffsets": "6148,6282,6393,6527,6642,6742,6859,7008,7132,7295,7381,7480,7573,7675,7795,7922,8026,8152,8283,8427,8595,8717,8834,8953,9080,9174,9271,9402,9539,9641,9753,9858,9984,10113,10216,10319,10400,10498,10594,10702,10789,10875,10994,11074,11158,11258,11360,11456,11554,11641,11748,11847,11948,12069,12149,12272,12390"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\457147f8f67deaf2a3ec4b5417c706c3\\transformed\\media3-exoplayer-1.4.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,202,275,344,426,501,602,697", "endColumns": "74,71,72,68,81,74,100,94,77", "endOffsets": "125,197,270,339,421,496,597,692,770"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4048,4123,4195,4268,4337,4419,4494,4595,4690", "endColumns": "74,71,72,68,81,74,100,94,77", "endOffsets": "4118,4190,4263,4332,4414,4489,4590,4685,4763"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values-my\\values-my.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,279,367,471,575,658,742,841,930,1012,1099,1185,1260,1349,1426,1499,1572,1653,1719", "endColumns": "93,87,103,103,82,83,98,88,81,86,85,74,88,76,72,72,80,65,125", "endOffsets": "274,362,466,570,653,737,836,925,1007,1094,1180,1255,1344,1421,1494,1567,1648,1714,1840"}, "to": {"startLines": "27,28,29,30,31,82,83,141,142,143,144,145,146,147,148,149,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1558,1652,1740,1844,1948,5833,5917,12395,12484,12566,12653,12739,12814,12903,12980,13053,13227,13308,13374", "endColumns": "93,87,103,103,82,83,98,88,81,86,85,74,88,76,72,72,80,65,125", "endOffsets": "1647,1735,1839,1943,2026,5912,6011,12479,12561,12648,12734,12809,12898,12975,13048,13121,13303,13369,13495"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "20,21,22,23,24,25,26,150", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "816,919,1023,1126,1228,1333,1439,13126", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "914,1018,1121,1223,1328,1434,1553,13222"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\e874b47f582ea1f96bd82c21770ae83f\\transformed\\media3-ui-1.4.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,292,491,687,771,853,940,1042,1138,1211,1278,1377,1472,1540,1607,1674,1741,1864,1985,2106,2177,2257,2330,2401,2489,2575,2640,2704,2757,2815,2865,2926,2984,3046,3119,3188,3253,3311,3375,3429,3491,3567,3643,3697", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,83,81,86,101,95,72,66,98,94,67,66,66,66,122,120,120,70,79,72,70,87,85,64,63,52,57,49,60,57,61,72,68,64,57,63,53,61,75,75,53,71", "endOffsets": "287,486,682,766,848,935,1037,1133,1206,1273,1372,1467,1535,1602,1669,1736,1859,1980,2101,2172,2252,2325,2396,2484,2570,2635,2699,2752,2810,2860,2921,2979,3041,3114,3183,3248,3306,3370,3424,3486,3562,3638,3692,3764"}, "to": {"startLines": "2,11,15,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,342,541,2031,2115,2197,2284,2386,2482,2555,2622,2721,2816,2884,2951,3018,3085,3208,3329,3450,3521,3601,3674,3745,3833,3919,3984,4768,4821,4879,4929,4990,5048,5110,5183,5252,5317,5375,5439,5493,5555,5631,5707,5761", "endLines": "10,14,18,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,83,81,86,101,95,72,66,98,94,67,66,66,66,122,120,120,70,79,72,70,87,85,64,63,52,57,49,60,57,61,72,68,64,57,63,53,61,75,75,53,71", "endOffsets": "337,536,732,2110,2192,2279,2381,2477,2550,2617,2716,2811,2879,2946,3013,3080,3203,3324,3445,3516,3596,3669,3740,3828,3914,3979,4043,4816,4874,4924,4985,5043,5105,5178,5247,5312,5370,5434,5488,5550,5626,5702,5756,5828"}}]}]}