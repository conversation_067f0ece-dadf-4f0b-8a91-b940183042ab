{"logs": [{"outputFile": "com.example.tik555.app-mergeReleaseResources-2:/values-es/values-es.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "20,21,22,23,24,25,26,150", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "788,887,989,1089,1187,1294,1400,13064", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "882,984,1084,1182,1289,1395,1515,13160"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\e874b47f582ea1f96bd82c21770ae83f\\transformed\\media3-ui-1.4.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,658,744,831,916,1012,1108,1183,1251,1346,1441,1507,1576,1642,1713,1821,1927,2034,2104,2191,2261,2341,2431,2522,2588,2652,2705,2763,2811,2870,2935,2997,3063,3135,3199,3260,3326,3379,3444,3523,3602,3660", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,107,105,106,69,86,69,79,89,90,65,63,52,57,47,58,64,61,65,71,63,60,65,52,64,78,78,57,69", "endOffsets": "280,466,653,739,826,911,1007,1103,1178,1246,1341,1436,1502,1571,1637,1708,1816,1922,2029,2099,2186,2256,2336,2426,2517,2583,2647,2700,2758,2806,2865,2930,2992,3058,3130,3194,3255,3321,3374,3439,3518,3597,3655,3725"}, "to": {"startLines": "2,11,15,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,1988,2074,2161,2246,2342,2438,2513,2581,2676,2771,2837,2906,2972,3043,3151,3257,3364,3434,3521,3591,3671,3761,3852,3918,4653,4706,4764,4812,4871,4936,4998,5064,5136,5200,5261,5327,5380,5445,5524,5603,5661", "endLines": "10,14,18,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,107,105,106,69,86,69,79,89,90,65,63,52,57,47,58,64,61,65,71,63,60,65,52,64,78,78,57,69", "endOffsets": "330,516,703,2069,2156,2241,2337,2433,2508,2576,2671,2766,2832,2901,2967,3038,3146,3252,3359,3429,3516,3586,3666,3756,3847,3913,3977,4701,4759,4807,4866,4931,4993,5059,5131,5195,5256,5322,5375,5440,5519,5598,5656,5726"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values-es\\values-es.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "186,282,364,462,565,654,733,826,918,1005,1091,1182,1259,1344,1420,1500,1576,1658,1728", "endColumns": "95,81,97,102,88,78,92,91,86,85,90,76,84,75,79,75,81,69,120", "endOffsets": "277,359,457,560,649,728,821,913,1000,1086,1177,1254,1339,1415,1495,1571,1653,1723,1844"}, "to": {"startLines": "27,28,29,30,31,82,83,141,142,143,144,145,146,147,148,149,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1520,1616,1698,1796,1899,5731,5810,12314,12406,12493,12579,12670,12747,12832,12908,12988,13165,13247,13317", "endColumns": "95,81,97,102,88,78,92,91,86,85,90,76,84,75,79,75,81,69,120", "endOffsets": "1611,1693,1791,1894,1983,5805,5898,12401,12488,12574,12665,12742,12827,12903,12983,13059,13242,13312,13433"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\457147f8f67deaf2a3ec4b5417c706c3\\transformed\\media3-exoplayer-1.4.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,201,266,340,417,484,571,657", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "133,196,261,335,412,479,566,652,721"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3982,4065,4128,4193,4267,4344,4411,4498,4584", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "4060,4123,4188,4262,4339,4406,4493,4579,4648"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,235", "endColumns": "79,99,101", "endOffsets": "130,230,332"}, "to": {"startLines": "19,154,155", "startColumns": "4,4,4", "startOffsets": "708,13438,13538", "endColumns": "79,99,101", "endOffsets": "783,13533,13635"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,418,539,639,739,856,999,1117,1265,1350,1457,1554,1656,1770,1888,2000,2138,2275,2419,2588,2724,2844,2966,3096,3194,3290,3411,3546,3649,3763,3878,4015,4156,4267,4372,4459,4555,4651,4767,4854,4940,5051,5134,5218,5319,5425,5525,5628,5717,5828,5929,6038,6157,6240,6357", "endColumns": "120,118,122,120,99,99,116,142,117,147,84,106,96,101,113,117,111,137,136,143,168,135,119,121,129,97,95,120,134,102,113,114,136,140,110,104,86,95,95,115,86,85,110,82,83,100,105,99,102,88,110,100,108,118,82,116,108", "endOffsets": "171,290,413,534,634,734,851,994,1112,1260,1345,1452,1549,1651,1765,1883,1995,2133,2270,2414,2583,2719,2839,2961,3091,3189,3285,3406,3541,3644,3758,3873,4010,4151,4262,4367,4454,4550,4646,4762,4849,4935,5046,5129,5213,5314,5420,5520,5623,5712,5823,5924,6033,6152,6235,6352,6461"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5903,6024,6143,6266,6387,6487,6587,6704,6847,6965,7113,7198,7305,7402,7504,7618,7736,7848,7986,8123,8267,8436,8572,8692,8814,8944,9042,9138,9259,9394,9497,9611,9726,9863,10004,10115,10220,10307,10403,10499,10615,10702,10788,10899,10982,11066,11167,11273,11373,11476,11565,11676,11777,11886,12005,12088,12205", "endColumns": "120,118,122,120,99,99,116,142,117,147,84,106,96,101,113,117,111,137,136,143,168,135,119,121,129,97,95,120,134,102,113,114,136,140,110,104,86,95,95,115,86,85,110,82,83,100,105,99,102,88,110,100,108,118,82,116,108", "endOffsets": "6019,6138,6261,6382,6482,6582,6699,6842,6960,7108,7193,7300,7397,7499,7613,7731,7843,7981,8118,8262,8431,8567,8687,8809,8939,9037,9133,9254,9389,9492,9606,9721,9858,9999,10110,10215,10302,10398,10494,10610,10697,10783,10894,10977,11061,11162,11268,11368,11471,11560,11671,11772,11881,12000,12083,12200,12309"}}]}]}