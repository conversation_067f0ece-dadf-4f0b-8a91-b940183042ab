C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:23: Warning: Redundant label can be removed [RedundantLabel]
            android:label="@string/app_name"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RedundantLabel":
   When an activity does not have a label attribute, it will use the one from
   the application tag. Since the application has already specified the same
   label, the label on this activity can be omitted.

C:\Users\<USER>\StudioProjects\tik555\gradle\libs.versions.toml:11: Warning: A newer version of androidx.media3:media3-database than 1.4.1 is available: 1.7.1 [GradleDependency]
media3 = "1.4.1"
         ~~~~~~~
C:\Users\<USER>\StudioProjects\tik555\gradle\libs.versions.toml:11: Warning: A newer version of androidx.media3:media3-datasource-okhttp than 1.4.1 is available: 1.7.1 [GradleDependency]
media3 = "1.4.1"
         ~~~~~~~
C:\Users\<USER>\StudioProjects\tik555\gradle\libs.versions.toml:11: Warning: A newer version of androidx.media3:media3-exoplayer than 1.4.1 is available: 1.7.1 [GradleDependency]
media3 = "1.4.1"
         ~~~~~~~
C:\Users\<USER>\StudioProjects\tik555\gradle\libs.versions.toml:11: Warning: A newer version of androidx.media3:media3-ui than 1.4.1 is available: 1.7.1 [GradleDependency]
media3 = "1.4.1"
         ~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

C:\Users\<USER>\StudioProjects\tik555\app\src\main\java\com\example\tik555\player\PlayerManager.kt:91: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
        val mediaSource = ProgressiveMediaSource.Factory(dataSourceFactory)
            ~~~~~~~~~~~
C:\Users\<USER>\StudioProjects\tik555\app\src\main\java\com\example\tik555\player\PlayerManager.kt:91: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
        val mediaSource = ProgressiveMediaSource.Factory(dataSourceFactory)
                                                 ~~~~~~~
C:\Users\<USER>\StudioProjects\tik555\app\src\main\java\com\example\tik555\player\PlayerManager.kt:92: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
            .createMediaSource(mediaItem)
             ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\StudioProjects\tik555\app\src\main\java\com\example\tik555\player\PlayerManager.kt:95: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
        playerWrapper.player.setMediaSource(mediaSource)
                             ~~~~~~~~~~~~~~

   Explanation for issues of type "UnsafeOptInUsageError":
   This API has been flagged as opt-in with error-level severity.

   Any declaration annotated with this marker is considered part of an
   unstable or
   otherwise non-standard API surface and its call sites should accept the
   opt-in
   aspect of it by using the @OptIn annotation, using the marker annotation
   --
   effectively causing further propagation of the opt-in aspect -- or
   configuring
   the UnsafeOptInUsageError check's options for project-wide opt-in.

   To configure project-wide opt-in, specify the opt-in option value in
   lint.xml
   as a comma-delimited list of opted-in annotations:

   <lint>
       <issue id="UnsafeOptInUsageError">
           <option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
       </issue>
   </lint>

   Vendor: Android Open Source Project
   Identifier: androidx.annotation.experimental
   Feedback: https://issuetracker.google.com/issues/new?component=459778

C:\Users\<USER>\StudioProjects\tik555\app\src\main\res\values\colors.xml:3: Warning: The resource R.color.purple_200 appears to be unused [UnusedResources]
    <color name="purple_200">#FFBB86FC</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\StudioProjects\tik555\app\src\main\res\values\colors.xml:4: Warning: The resource R.color.purple_500 appears to be unused [UnusedResources]
    <color name="purple_500">#FF6200EE</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\StudioProjects\tik555\app\src\main\res\values\colors.xml:5: Warning: The resource R.color.purple_700 appears to be unused [UnusedResources]
    <color name="purple_700">#FF3700B3</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\StudioProjects\tik555\app\src\main\res\values\colors.xml:6: Warning: The resource R.color.teal_200 appears to be unused [UnusedResources]
    <color name="teal_200">#FF03DAC5</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\StudioProjects\tik555\app\src\main\res\values\colors.xml:7: Warning: The resource R.color.teal_700 appears to be unused [UnusedResources]
    <color name="teal_700">#FF018786</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\StudioProjects\tik555\app\src\main\res\values\colors.xml:8: Warning: The resource R.color.black appears to be unused [UnusedResources]
    <color name="black">#FF000000</color>
           ~~~~~~~~~~~~
C:\Users\<USER>\StudioProjects\tik555\app\src\main\res\values\colors.xml:9: Warning: The resource R.color.white appears to be unused [UnusedResources]
    <color name="white">#FFFFFFFF</color>
           ~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

   Available options:

   **skip-libraries** (default is true):
   Whether the unused resource check should skip reporting unused resources in libraries.

   Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with `checkDependencies=true`).

   However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UnusedResources">
           <option name="skip-libraries" value="true" />
       </issue>
   </lint>
   ```

4 errors, 12 warnings
