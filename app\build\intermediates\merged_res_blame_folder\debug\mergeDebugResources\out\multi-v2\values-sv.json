{"logs": [{"outputFile": "com.example.tik555.app-mergeDebugResources-2:/values-sv/values-sv.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\17d8797a605bf267136b984c4f4c6900\\transformed\\media3-ui-1.7.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,480,674,760,847,930,1018,1102,1170,1234,1332,1430,1495,1563,1629,1702,1822,1942,2060,2135,2217,2293,2361,2451,2542,2607,2671,2724,2784,2832,2893,2957,3028,3092,3157,3222,3281,3346,3398,3458,3541,3624,3676", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,86,82,87,83,67,63,97,97,64,67,65,72,119,119,117,74,81,75,67,89,90,64,63,52,59,47,60,63,70,63,64,64,58,64,51,59,82,82,51,63", "endOffsets": "280,475,669,755,842,925,1013,1097,1165,1229,1327,1425,1490,1558,1624,1697,1817,1937,2055,2130,2212,2288,2356,2446,2537,2602,2666,2719,2779,2827,2888,2952,3023,3087,3152,3217,3276,3341,3393,3453,3536,3619,3671,3735"}, "to": {"startLines": "2,11,15,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,530,1991,2077,2164,2247,2335,2419,2487,2551,2649,2747,2812,2880,2946,3019,3139,3259,3377,3452,3534,3610,3678,3768,3859,3924,4671,4724,4784,4832,4893,4957,5028,5092,5157,5222,5281,5346,5398,5458,5541,5624,5676", "endLines": "10,14,18,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,85,86,82,87,83,67,63,97,97,64,67,65,72,119,119,117,74,81,75,67,89,90,64,63,52,59,47,60,63,70,63,64,64,58,64,51,59,82,82,51,63", "endOffsets": "330,525,719,2072,2159,2242,2330,2414,2482,2546,2644,2742,2807,2875,2941,3014,3134,3254,3372,3447,3529,3605,3673,3763,3854,3919,3983,4719,4779,4827,4888,4952,5023,5087,5152,5217,5276,5341,5393,5453,5536,5619,5671,5735"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\8a2e8e7142c38a8f2ee7133e4de5b14c\\transformed\\material-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "141", "startColumns": "4", "startOffsets": "12178", "endColumns": "89", "endOffsets": "12263"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,327,434,566,682,778,891,1035,1159,1314,1399,1498,1588,1682,1796,1918,2022,2155,2282,2417,2589,2717,2835,2961,3081,3172,3270,3388,3527,3623,3731,3834,3967,4110,4216,4313,4393,4491,4583,4699,4783,4868,4969,5049,5134,5233,5333,5428,5528,5615,5719,5820,5924,6046,6126,6230", "endColumns": "138,132,106,131,115,95,112,143,123,154,84,98,89,93,113,121,103,132,126,134,171,127,117,125,119,90,97,117,138,95,107,102,132,142,105,96,79,97,91,115,83,84,100,79,84,98,99,94,99,86,103,100,103,121,79,103,98", "endOffsets": "189,322,429,561,677,773,886,1030,1154,1309,1394,1493,1583,1677,1791,1913,2017,2150,2277,2412,2584,2712,2830,2956,3076,3167,3265,3383,3522,3618,3726,3829,3962,4105,4211,4308,4388,4486,4578,4694,4778,4863,4964,5044,5129,5228,5328,5423,5523,5610,5714,5815,5919,6041,6121,6225,6324"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5904,6043,6176,6283,6415,6531,6627,6740,6884,7008,7163,7248,7347,7437,7531,7645,7767,7871,8004,8131,8266,8438,8566,8684,8810,8930,9021,9119,9237,9376,9472,9580,9683,9816,9959,10065,10162,10242,10340,10432,10548,10632,10717,10818,10898,10983,11082,11182,11277,11377,11464,11568,11669,11773,11895,11975,12079", "endColumns": "138,132,106,131,115,95,112,143,123,154,84,98,89,93,113,121,103,132,126,134,171,127,117,125,119,90,97,117,138,95,107,102,132,142,105,96,79,97,91,115,83,84,100,79,84,98,99,94,99,86,103,100,103,121,79,103,98", "endOffsets": "6038,6171,6278,6410,6526,6622,6735,6879,7003,7158,7243,7342,7432,7526,7640,7762,7866,7999,8126,8261,8433,8561,8679,8805,8925,9016,9114,9232,9371,9467,9575,9678,9811,9954,10060,10157,10237,10335,10427,10543,10627,10712,10813,10893,10978,11077,11177,11272,11372,11459,11563,11664,11768,11890,11970,12074,12173"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,220", "endColumns": "74,89,88", "endOffsets": "125,215,304"}, "to": {"startLines": "19,155,156", "startColumns": "4,4,4", "startOffsets": "724,13350,13440", "endColumns": "74,89,88", "endOffsets": "794,13435,13524"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\dc0228f35d5d9920c8fe531ae3e0365a\\transformed\\media3-exoplayer-1.7.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,191,255,330,411,485,579,665", "endColumns": "72,62,63,74,80,73,93,85,72", "endOffsets": "123,186,250,325,406,480,574,660,733"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3988,4061,4124,4188,4263,4344,4418,4512,4598", "endColumns": "72,62,63,74,80,73,93,85,72", "endOffsets": "4056,4119,4183,4258,4339,4413,4507,4593,4666"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "20,21,22,23,24,25,26,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "799,894,996,1094,1193,1301,1406,12984", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "889,991,1089,1188,1296,1401,1522,13080"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "181,274,362,458,557,645,721,809,898,979,1065,1155,1224,1310,1384,1455,1525,1603,1670", "endColumns": "92,87,95,98,87,75,87,88,80,85,89,68,85,73,70,69,77,66,119", "endOffsets": "269,357,453,552,640,716,804,893,974,1060,1150,1219,1305,1379,1450,1520,1598,1665,1785"}, "to": {"startLines": "27,28,29,30,31,82,83,142,143,144,145,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1527,1620,1708,1804,1903,5740,5816,12268,12357,12438,12524,12614,12683,12769,12843,12914,13085,13163,13230", "endColumns": "92,87,95,98,87,75,87,88,80,85,89,68,85,73,70,69,77,66,119", "endOffsets": "1615,1703,1799,1898,1986,5811,5899,12352,12433,12519,12609,12678,12764,12838,12909,12979,13158,13225,13345"}}]}]}