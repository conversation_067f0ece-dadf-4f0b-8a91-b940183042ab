package com.example.tik555.ui.components

import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.ui.PlayerView
import com.example.tik555.data.VideoItem
import com.example.tik555.player.PlayerManager

/**
 * 视频播放器组件
 * 无UI控件的纯播放器视图
 */
@Composable
fun VideoPlayerView(
    videoItem: VideoItem,
    videoIndex: Int,
    playerManager: PlayerManager,
    isCurrentVideo: <PERSON><PERSON>an,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    
    // 获取播放器
    val playerWrapper = remember(videoIndex, videoItem) {
        playerManager.getPlayerForVideo(videoIndex, videoItem)
    }
    
    // 根据视频比例计算显示方式
    val aspectRatio = videoItem.aspectRatio
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black),
        contentAlignment = Alignment.Center
    ) {
        AndroidView(
            factory = { context ->
                PlayerView(context).apply {
                    // 关闭所有UI控件
                    useController = false
                    
                    // 设置布局参数
                    layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                    
                    // 绑定播放器
                    player = playerWrapper.player
                }
            },
            modifier = if (aspectRatio > 1f) {
                // 横屏视频，按宽度适配
                Modifier
                    .fillMaxWidth()
                    .aspectRatio(aspectRatio)
            } else {
                // 竖屏视频，按高度适配
                Modifier
                    .fillMaxSize()
            },
            update = { playerView ->
                playerView.player = playerWrapper.player
            }
        )
    }
    
    // 处理播放状态
    DisposableEffect(isCurrentVideo) {
        if (isCurrentVideo) {
            playerManager.playVideo(videoIndex)
        } else {
            playerManager.pauseVideo(videoIndex)
        }
        
        onDispose {
            if (isCurrentVideo) {
                playerManager.pauseVideo(videoIndex)
            }
        }
    }
}
