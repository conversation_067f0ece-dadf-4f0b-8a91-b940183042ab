1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.tik555"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission
13-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:7:5-8:38
14        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
14-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:7:22-78
15        android:maxSdkVersion="28" />
15-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:8:9-35
16
17    <permission
17-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
18        android:name="com.example.tik555.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
18-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
19        android:protectionLevel="signature" />
19-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
20
21    <uses-permission android:name="com.example.tik555.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
21-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
21-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
22
23    <application
23-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:10:5-31:19
24        android:allowBackup="true"
24-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:11:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
26        android:dataExtractionRules="@xml/data_extraction_rules"
26-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:12:9-65
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:13:9-54
29        android:icon="@mipmap/ic_launcher"
29-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:14:9-43
30        android:label="@string/app_name"
30-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:15:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:16:9-54
32        android:supportsRtl="true"
32-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:17:9-35
33        android:theme="@style/Theme.Tik555" >
33-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:18:9-44
34        <activity
34-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:20:9-30:20
35            android:name="com.example.tik555.MainActivity"
35-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:21:13-41
36            android:exported="true"
36-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:22:13-36
37            android:label="@string/app_name"
37-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:23:13-45
38            android:theme="@style/Theme.Tik555" >
38-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:24:13-48
39            <intent-filter>
39-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:25:13-29:29
40                <action android:name="android.intent.action.MAIN" />
40-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:26:17-69
40-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:26:25-66
41
42                <category android:name="android.intent.category.LAUNCHER" />
42-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:28:17-77
42-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:28:27-74
43            </intent-filter>
44        </activity>
45
46        <provider
46-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
47            android:name="androidx.startup.InitializationProvider"
47-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
48            android:authorities="com.example.tik555.androidx-startup"
48-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
49            android:exported="false" >
49-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
50            <meta-data
50-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
51                android:name="androidx.emoji2.text.EmojiCompatInitializer"
51-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
52                android:value="androidx.startup" />
52-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
53            <meta-data
53-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\92a51bc7c02ff55fe83543917b2f6d99\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
54                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
54-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\92a51bc7c02ff55fe83543917b2f6d99\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
55                android:value="androidx.startup" />
55-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\92a51bc7c02ff55fe83543917b2f6d99\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
56            <meta-data
56-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
57                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
57-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
58                android:value="androidx.startup" />
58-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
59        </provider>
60
61        <receiver
61-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
62            android:name="androidx.profileinstaller.ProfileInstallReceiver"
62-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
63            android:directBootAware="false"
63-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
64            android:enabled="true"
64-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
65            android:exported="true"
65-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
66            android:permission="android.permission.DUMP" >
66-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
67            <intent-filter>
67-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
68                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
68-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
68-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
69            </intent-filter>
70            <intent-filter>
70-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
71                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
71-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
71-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
72            </intent-filter>
73            <intent-filter>
73-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
74                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
74-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
74-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
75            </intent-filter>
76            <intent-filter>
76-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
77                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
77-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
77-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
78            </intent-filter>
79        </receiver>
80    </application>
81
82</manifest>
