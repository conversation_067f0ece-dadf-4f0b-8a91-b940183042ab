{"logs": [{"outputFile": "com.example.tik555.app-mergeDebugResources-2:/values-ja/values-ja.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\17d8797a605bf267136b984c4f4c6900\\transformed\\media3-ui-1.7.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,281,444,604,676,747,816,895,973,1039,1100,1178,1255,1319,1380,1439,1504,1591,1678,1766,1831,1897,1962,2026,2107,2187,2248,2311,2363,2421,2469,2530,2586,2648,2705,2765,2821,2877,2940,2990,3048,3120,3192,3241", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,71,70,68,78,77,65,60,77,76,63,60,58,64,86,86,87,64,65,64,63,80,79,60,62,51,57,47,60,55,61,56,59,55,55,62,49,57,71,71,48,59", "endOffsets": "276,439,599,671,742,811,890,968,1034,1095,1173,1250,1314,1375,1434,1499,1586,1673,1761,1826,1892,1957,2021,2102,2182,2243,2306,2358,2416,2464,2525,2581,2643,2700,2760,2816,2872,2935,2985,3043,3115,3187,3236,3296"}, "to": {"startLines": "2,11,15,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,331,494,1827,1899,1970,2039,2118,2196,2262,2323,2401,2478,2542,2603,2662,2727,2814,2901,2989,3054,3120,3185,3249,3330,3410,3471,4141,4193,4251,4299,4360,4416,4478,4535,4595,4651,4707,4770,4820,4878,4950,5022,5071", "endLines": "10,14,18,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,71,70,68,78,77,65,60,77,76,63,60,58,64,86,86,87,64,65,64,63,80,79,60,62,51,57,47,60,55,61,56,59,55,55,62,49,57,71,71,48,59", "endOffsets": "326,489,649,1894,1965,2034,2113,2191,2257,2318,2396,2473,2537,2598,2657,2722,2809,2896,2984,3049,3115,3180,3244,3325,3405,3466,3529,4188,4246,4294,4355,4411,4473,4530,4590,4646,4702,4765,4815,4873,4945,5017,5066,5126"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\8a2e8e7142c38a8f2ee7133e4de5b14c\\transformed\\material-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "86", "endOffsets": "137"}, "to": {"startLines": "141", "startColumns": "4", "startOffsets": "10989", "endColumns": "86", "endOffsets": "11071"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "20,21,22,23,24,25,26,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "725,817,917,1011,1107,1200,1293,11753", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "812,912,1006,1102,1195,1288,1389,11849"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "177,263,341,430,527,610,688,766,851,926,1000,1076,1145,1228,1301,1373,1443,1519,1584", "endColumns": "85,77,88,96,82,77,77,84,74,73,75,68,82,72,71,69,75,64,116", "endOffsets": "258,336,425,522,605,683,761,846,921,995,1071,1140,1223,1296,1368,1438,1514,1579,1696"}, "to": {"startLines": "27,28,29,30,31,82,83,142,143,144,145,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1394,1480,1558,1647,1744,5131,5209,11076,11161,11236,11310,11386,11455,11538,11611,11683,11854,11930,11995", "endColumns": "85,77,88,96,82,77,77,84,74,73,75,68,82,72,71,69,75,64,116", "endOffsets": "1475,1553,1642,1739,1822,5204,5282,11156,11231,11305,11381,11450,11533,11606,11678,11748,11925,11990,12107"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,273,382,488,581,671,778,892,1000,1124,1206,1303,1388,1478,1585,1698,1800,1924,2046,2160,2287,2397,2498,2602,2710,2796,2891,2999,3111,3202,3299,3396,3517,3643,3742,3834,3909,4002,4094,4195,4278,4361,4458,4538,4620,4718,4813,4906,5003,5086,5182,5277,5375,5486,5566,5663", "endColumns": "110,106,108,105,92,89,106,113,107,123,81,96,84,89,106,112,101,123,121,113,126,109,100,103,107,85,94,107,111,90,96,96,120,125,98,91,74,92,91,100,82,82,96,79,81,97,94,92,96,82,95,94,97,110,79,96,93", "endOffsets": "161,268,377,483,576,666,773,887,995,1119,1201,1298,1383,1473,1580,1693,1795,1919,2041,2155,2282,2392,2493,2597,2705,2791,2886,2994,3106,3197,3294,3391,3512,3638,3737,3829,3904,3997,4089,4190,4273,4356,4453,4533,4615,4713,4808,4901,4998,5081,5177,5272,5370,5481,5561,5658,5752"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5287,5398,5505,5614,5720,5813,5903,6010,6124,6232,6356,6438,6535,6620,6710,6817,6930,7032,7156,7278,7392,7519,7629,7730,7834,7942,8028,8123,8231,8343,8434,8531,8628,8749,8875,8974,9066,9141,9234,9326,9427,9510,9593,9690,9770,9852,9950,10045,10138,10235,10318,10414,10509,10607,10718,10798,10895", "endColumns": "110,106,108,105,92,89,106,113,107,123,81,96,84,89,106,112,101,123,121,113,126,109,100,103,107,85,94,107,111,90,96,96,120,125,98,91,74,92,91,100,82,82,96,79,81,97,94,92,96,82,95,94,97,110,79,96,93", "endOffsets": "5393,5500,5609,5715,5808,5898,6005,6119,6227,6351,6433,6530,6615,6705,6812,6925,7027,7151,7273,7387,7514,7624,7725,7829,7937,8023,8118,8226,8338,8429,8526,8623,8744,8870,8969,9061,9136,9229,9321,9422,9505,9588,9685,9765,9847,9945,10040,10133,10230,10313,10409,10504,10602,10713,10793,10890,10984"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\dc0228f35d5d9920c8fe531ae3e0365a\\transformed\\media3-exoplayer-1.7.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,180,245,307,381,440,520,597", "endColumns": "64,59,64,61,73,58,79,76,64", "endOffsets": "115,175,240,302,376,435,515,592,657"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3534,3599,3659,3724,3786,3860,3919,3999,4076", "endColumns": "64,59,64,61,73,58,79,76,64", "endOffsets": "3594,3654,3719,3781,3855,3914,3994,4071,4136"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,126,211", "endColumns": "70,84,81", "endOffsets": "121,206,288"}, "to": {"startLines": "19,155,156", "startColumns": "4,4,4", "startOffsets": "654,12112,12197", "endColumns": "70,84,81", "endOffsets": "720,12192,12274"}}]}]}