package com.example.tik555.ui.screens

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.pager.VerticalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.tik555.player.PlayerManager
import com.example.tik555.ui.components.DebugInfoOverlay
import com.example.tik555.ui.components.VideoPlayerView
import com.example.tik555.viewmodel.VideoViewModel
import kotlinx.coroutines.flow.distinctUntilChanged

/**
 * 主视频页面
 * 使用VerticalPager实现垂直滑动的短视频播放
 */
@Composable
fun VideoScreen(
    modifier: Modifier = Modifier,
    viewModel: VideoViewModel = viewModel()
) {
    val context = LocalContext.current
    val videoList by viewModel.videoList.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val playerInfo by viewModel.playerInfo.collectAsState()
    
    // 创建播放器管理器
    val playerManager = remember {
        PlayerManager(context)
    }
    
    // 更新ViewModel中的播放器管理器
    LaunchedEffect(playerManager) {
        viewModel.setPlayerManager(playerManager)
    }
    
    // 初始化加载视频列表
    LaunchedEffect(Unit) {
        viewModel.loadInitialVideos()
    }
    
    if (videoList.isNotEmpty()) {
        val pagerState = rememberPagerState(
            initialPage = 0,
            pageCount = { videoList.size }
        )
        
        // 监听页面变化
        LaunchedEffect(pagerState) {
            snapshotFlow { pagerState.currentPage }
                .distinctUntilChanged()
                .collect { currentPage ->
                    // 更新当前页面
                    viewModel.updateCurrentPage(currentPage)
                    
                    // 预加载视频
                    viewModel.preloadVideos(currentPage)
                    
                    // 检查是否需要加载更多视频
                    if (currentPage >= videoList.size - 3) {
                        viewModel.loadMoreVideos()
                    }
                }
        }
        
        Box(modifier = modifier.fillMaxSize()) {
            VerticalPager(
                state = pagerState,
                modifier = Modifier.fillMaxSize()
            ) { pageIndex ->
                val videoItem = videoList[pageIndex]
                val isCurrentVideo = pageIndex == pagerState.currentPage
                
                VideoPlayerView(
                    videoItem = videoItem,
                    videoIndex = pageIndex,
                    playerManager = playerManager,
                    isCurrentVideo = isCurrentVideo,
                    modifier = Modifier.fillMaxSize()
                )
            }
            
            // 调试信息覆盖层
            DebugInfoOverlay(
                currentVideoIndex = pagerState.currentPage,
                currentVideoItem = videoList.getOrNull(pagerState.currentPage),
                playerInfo = playerInfo
            )
        }
    }
    
    // 清理资源
    DisposableEffect(Unit) {
        onDispose {
            playerManager.release()
        }
    }
}
