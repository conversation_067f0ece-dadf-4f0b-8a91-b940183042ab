<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="incidents">

    <incident
        id="RedundantLabel"
        severity="warning"
        message="Redundant label can be removed">
        <fix-attribute
            description="Delete label"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="label"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="23"
            column="13"
            startOffset="971"
            endLine="23"
            endColumn="45"
            endOffset="1003"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.media3:media3-database than 1.4.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.4.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="$HOME/StudioProjects/tik555/gradle/libs.versions.toml"
            line="11"
            column="10"
            startOffset="218"
            endLine="11"
            endColumn="17"
            endOffset="225"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.media3:media3-datasource-okhttp than 1.4.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.4.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="$HOME/StudioProjects/tik555/gradle/libs.versions.toml"
            line="11"
            column="10"
            startOffset="218"
            endLine="11"
            endColumn="17"
            endOffset="225"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.media3:media3-exoplayer than 1.4.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.4.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="$HOME/StudioProjects/tik555/gradle/libs.versions.toml"
            line="11"
            column="10"
            startOffset="218"
            endLine="11"
            endColumn="17"
            endOffset="225"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.media3:media3-ui than 1.4.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.4.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="$HOME/StudioProjects/tik555/gradle/libs.versions.toml"
            line="11"
            column="10"
            startOffset="218"
            endLine="11"
            endColumn="17"
            endOffset="225"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-alternatives>
            <annotate
                description="Add &apos;@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)&apos; annotation to &apos;bindVideoToPlayer&apos;"
                source="@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/tik555/player/PlayerManager.kt"
                    startOffset="2291"
                    endOffset="2966"/>
            </annotate>
            <annotate
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to &apos;bindVideoToPlayer&apos;"
                source="@androidx.media3.common.util.UnstableApi"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/tik555/player/PlayerManager.kt"
                    startOffset="2291"
                    endOffset="2966"/>
            </annotate>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/tik555/player/PlayerManager.kt"
            line="91"
            column="13"
            startOffset="2651"
            endLine="91"
            endColumn="24"
            endOffset="2662"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-alternatives>
            <annotate
                description="Add &apos;@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)&apos; annotation to &apos;bindVideoToPlayer&apos;"
                source="@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/tik555/player/PlayerManager.kt"
                    startOffset="2291"
                    endOffset="2966"/>
            </annotate>
            <annotate
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to &apos;bindVideoToPlayer&apos;"
                source="@androidx.media3.common.util.UnstableApi"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/tik555/player/PlayerManager.kt"
                    startOffset="2291"
                    endOffset="2966"/>
            </annotate>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/tik555/player/PlayerManager.kt"
            line="91"
            column="50"
            startOffset="2688"
            endLine="91"
            endColumn="57"
            endOffset="2695"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-alternatives>
            <annotate
                description="Add &apos;@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)&apos; annotation to &apos;bindVideoToPlayer&apos;"
                source="@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/tik555/player/PlayerManager.kt"
                    startOffset="2291"
                    endOffset="2966"/>
            </annotate>
            <annotate
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to &apos;bindVideoToPlayer&apos;"
                source="@androidx.media3.common.util.UnstableApi"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/tik555/player/PlayerManager.kt"
                    startOffset="2291"
                    endOffset="2966"/>
            </annotate>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/tik555/player/PlayerManager.kt"
            line="92"
            column="14"
            startOffset="2728"
            endLine="92"
            endColumn="31"
            endOffset="2745"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-alternatives>
            <annotate
                description="Add &apos;@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)&apos; annotation to &apos;bindVideoToPlayer&apos;"
                source="@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/tik555/player/PlayerManager.kt"
                    startOffset="2291"
                    endOffset="2966"/>
            </annotate>
            <annotate
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to &apos;bindVideoToPlayer&apos;"
                source="@androidx.media3.common.util.UnstableApi"
                replace="true">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/tik555/player/PlayerManager.kt"
                    startOffset="2291"
                    endOffset="2966"/>
            </annotate>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/tik555/player/PlayerManager.kt"
            line="95"
            column="30"
            startOffset="2815"
            endLine="95"
            endColumn="44"
            endOffset="2829"/>
    </incident>

</incidents>
