-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:2:1-33:12
INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:2:1-33:12
INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:2:1-33:12
INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:2:1-33:12
MERGED from [androidx.media3:media3-ui:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\17d8797a605bf267136b984c4f4c6900\transformed\media3-ui-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource-okhttp:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\8d185c8b4f65f26147923538da12f7aa\transformed\media3-datasource-okhttp-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\7189d54549f7cf4ac944037540103fb2\transformed\media3-datasource-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\9e560f73e46074f641f8e22cd4725ab1\transformed\media3-database-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\988d55c47a54375fce8bb7baa8164bc5\transformed\media3-extractor-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\8b105724e120010a5c0033eff84d88cf\transformed\media3-container-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\dea3fc0be4c62defd39610909cc03d87\transformed\media3-decoder-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\918da196e5195f6decf7e8ab6c7f81d8\transformed\media3-common-1.7.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\dc0228f35d5d9920c8fe531ae3e0365a\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\ad908614da85acc9911c8dc4731a8333\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] D:\SDK\.gradle\caches\8.11.1\transforms\cfbe6762a9ebca8f43d22b008e11bcd0\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\8a2e8e7142c38a8f2ee7133e4de5b14c\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\1434ae527be5227b50dea6cff65aeecd\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\328507aa9e591fb7b26767f099dcfb6e\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\a24328e76c4ea592d2d3af0c8a2b9c6e\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\a805b3df82c0f1d10d3488a563f12d73\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\c47dbf3d10b6776e22b3f43f2fc410c6\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4c4454d6cf1a0487e1e6c8a26bd343ad\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\81c1444e3af2b7d8ecac2357f5832d6b\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\f1e68065b061fd3f9688025e9d310d54\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\367453924602fce52a1da29b574a3bf6\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\d3cf6caf4f6734eb2de3b4ee5e489228\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.animation:animation-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\936ae82b5d3e3566df7ca9825d10d95d\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\753fd0e6095def29bc665c94637b31cd\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\5b4ba3bb7e234dc8495fb2bbdf42cb63\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\c92be2f7345f54bffbd3fef688cfc3ea\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\7b5628fa636fa2f883564c96afca4908\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] D:\SDK\.gradle\caches\8.11.1\transforms\00573c5762cd403b25d6a3b98063a7ae\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\92a51bc7c02ff55fe83543917b2f6d99\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\a58900e3e5094897d10ae5fd311db10e\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\ce0befb1356a7a9ef092407c84b56366\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\9a9ea0066305f6c8846b99ed4c813481\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\1a91f0e1379b47d50c6190e7bb707fab\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\a8b41728e4b0de1ded866e01dadfdf54\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\db34985b86d559424c1a8bc12904a68c\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\ab3d1566985d0eb315118d7648207d2e\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\a6dfb9cc4481f505e665b064184db016\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\f79b6a98d14b9cbcfa7708c8935b6592\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\e5ac02f54f3d2dd10311886e9484c473\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] D:\SDK\.gradle\caches\8.11.1\transforms\6067fae33e9a7edfaf370d73067214e1\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\f1146f774d6258b00f26aa6ad8af6b00\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4dd3fdaaff8835a6d69528f823645a30\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.activity:activity:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\2a7f45898462c893c925156d163f656f\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\f1e5017d927147ee57550e382728a21c\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\d2f3baf9da570c6bc7841b93e17ab012\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\d600dd0990b6dcf9425752e5cf74f8fb\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\7af4b4f491a6596e64d50ced738265c5\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\97500f0365f817c48aeef75c3624e1ff\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\9aeaa20feb746deed08812d1648685c4\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\f26d33392aad83b4a93bce8ea0c88368\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\edea32bee283cb823eb7cb435e4a8ee9\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\98ab0d71b60d4f922b7af62eb452e747\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\SDK\.gradle\caches\8.11.1\transforms\5858ef100c4cfcd4b2676b4bc0b4d5ed\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\6ec472f0f3951334fe806e2ee6a2ef6d\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\418d17911481cf6fb34ad14d796f81b1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\975c1d8adc23a6c6f997efa5b6192589\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\58b6d2d79e584f1872a1217950ffd323\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:6:5-79
MERGED from [androidx.media3:media3-common:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\918da196e5195f6decf7e8ab6c7f81d8\transformed\media3-common-1.7.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\918da196e5195f6decf7e8ab6c7f81d8\transformed\media3-common-1.7.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\dc0228f35d5d9920c8fe531ae3e0365a\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\dc0228f35d5d9920c8fe531ae3e0365a\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:7:5-8:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:8:9-35
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:7:22-78
application
ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:10:5-31:19
INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:10:5-31:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\d3cf6caf4f6734eb2de3b4ee5e489228\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\d3cf6caf4f6734eb2de3b4ee5e489228\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\92a51bc7c02ff55fe83543917b2f6d99\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\92a51bc7c02ff55fe83543917b2f6d99\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4dd3fdaaff8835a6d69528f823645a30\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4dd3fdaaff8835a6d69528f823645a30\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\98ab0d71b60d4f922b7af62eb452e747\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\98ab0d71b60d4f922b7af62eb452e747\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\418d17911481cf6fb34ad14d796f81b1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\418d17911481cf6fb34ad14d796f81b1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:17:9-35
	android:label
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:15:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:13:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:16:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:19:9-29
	android:icon
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:14:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:11:9-35
	android:theme
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:18:9-44
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:12:9-65
activity#com.example.tik555.MainActivity
ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:20:9-30:20
	android:label
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:23:13-45
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:22:13-36
	android:theme
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:24:13-48
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:21:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:25:13-29:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:26:17-69
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:26:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:28:17-77
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:28:27-74
uses-sdk
INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml
MERGED from [androidx.media3:media3-ui:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\17d8797a605bf267136b984c4f4c6900\transformed\media3-ui-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\17d8797a605bf267136b984c4f4c6900\transformed\media3-ui-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\8d185c8b4f65f26147923538da12f7aa\transformed\media3-datasource-okhttp-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\8d185c8b4f65f26147923538da12f7aa\transformed\media3-datasource-okhttp-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\7189d54549f7cf4ac944037540103fb2\transformed\media3-datasource-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\7189d54549f7cf4ac944037540103fb2\transformed\media3-datasource-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\9e560f73e46074f641f8e22cd4725ab1\transformed\media3-database-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\9e560f73e46074f641f8e22cd4725ab1\transformed\media3-database-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\988d55c47a54375fce8bb7baa8164bc5\transformed\media3-extractor-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\988d55c47a54375fce8bb7baa8164bc5\transformed\media3-extractor-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\8b105724e120010a5c0033eff84d88cf\transformed\media3-container-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\8b105724e120010a5c0033eff84d88cf\transformed\media3-container-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\dea3fc0be4c62defd39610909cc03d87\transformed\media3-decoder-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\dea3fc0be4c62defd39610909cc03d87\transformed\media3-decoder-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\918da196e5195f6decf7e8ab6c7f81d8\transformed\media3-common-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\918da196e5195f6decf7e8ab6c7f81d8\transformed\media3-common-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\dc0228f35d5d9920c8fe531ae3e0365a\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\dc0228f35d5d9920c8fe531ae3e0365a\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\ad908614da85acc9911c8dc4731a8333\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\ad908614da85acc9911c8dc4731a8333\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] D:\SDK\.gradle\caches\8.11.1\transforms\cfbe6762a9ebca8f43d22b008e11bcd0\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] D:\SDK\.gradle\caches\8.11.1\transforms\cfbe6762a9ebca8f43d22b008e11bcd0\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\8a2e8e7142c38a8f2ee7133e4de5b14c\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\8a2e8e7142c38a8f2ee7133e4de5b14c\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\1434ae527be5227b50dea6cff65aeecd\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\1434ae527be5227b50dea6cff65aeecd\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\328507aa9e591fb7b26767f099dcfb6e\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\328507aa9e591fb7b26767f099dcfb6e\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\a24328e76c4ea592d2d3af0c8a2b9c6e\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\a24328e76c4ea592d2d3af0c8a2b9c6e\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\a805b3df82c0f1d10d3488a563f12d73\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\a805b3df82c0f1d10d3488a563f12d73\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\c47dbf3d10b6776e22b3f43f2fc410c6\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\c47dbf3d10b6776e22b3f43f2fc410c6\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4c4454d6cf1a0487e1e6c8a26bd343ad\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4c4454d6cf1a0487e1e6c8a26bd343ad\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\81c1444e3af2b7d8ecac2357f5832d6b\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\81c1444e3af2b7d8ecac2357f5832d6b\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\f1e68065b061fd3f9688025e9d310d54\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\f1e68065b061fd3f9688025e9d310d54\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\367453924602fce52a1da29b574a3bf6\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\367453924602fce52a1da29b574a3bf6\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\d3cf6caf4f6734eb2de3b4ee5e489228\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\d3cf6caf4f6734eb2de3b4ee5e489228\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\936ae82b5d3e3566df7ca9825d10d95d\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\936ae82b5d3e3566df7ca9825d10d95d\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\753fd0e6095def29bc665c94637b31cd\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\753fd0e6095def29bc665c94637b31cd\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\5b4ba3bb7e234dc8495fb2bbdf42cb63\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\5b4ba3bb7e234dc8495fb2bbdf42cb63\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\c92be2f7345f54bffbd3fef688cfc3ea\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\c92be2f7345f54bffbd3fef688cfc3ea\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\7b5628fa636fa2f883564c96afca4908\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\7b5628fa636fa2f883564c96afca4908\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] D:\SDK\.gradle\caches\8.11.1\transforms\00573c5762cd403b25d6a3b98063a7ae\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] D:\SDK\.gradle\caches\8.11.1\transforms\00573c5762cd403b25d6a3b98063a7ae\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\92a51bc7c02ff55fe83543917b2f6d99\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\92a51bc7c02ff55fe83543917b2f6d99\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\a58900e3e5094897d10ae5fd311db10e\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\a58900e3e5094897d10ae5fd311db10e\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\ce0befb1356a7a9ef092407c84b56366\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\ce0befb1356a7a9ef092407c84b56366\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\9a9ea0066305f6c8846b99ed4c813481\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\9a9ea0066305f6c8846b99ed4c813481\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\1a91f0e1379b47d50c6190e7bb707fab\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\1a91f0e1379b47d50c6190e7bb707fab\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\a8b41728e4b0de1ded866e01dadfdf54\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\a8b41728e4b0de1ded866e01dadfdf54\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\db34985b86d559424c1a8bc12904a68c\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\db34985b86d559424c1a8bc12904a68c\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\ab3d1566985d0eb315118d7648207d2e\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\ab3d1566985d0eb315118d7648207d2e\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\a6dfb9cc4481f505e665b064184db016\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\a6dfb9cc4481f505e665b064184db016\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\f79b6a98d14b9cbcfa7708c8935b6592\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\f79b6a98d14b9cbcfa7708c8935b6592\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\e5ac02f54f3d2dd10311886e9484c473\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\e5ac02f54f3d2dd10311886e9484c473\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] D:\SDK\.gradle\caches\8.11.1\transforms\6067fae33e9a7edfaf370d73067214e1\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] D:\SDK\.gradle\caches\8.11.1\transforms\6067fae33e9a7edfaf370d73067214e1\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\f1146f774d6258b00f26aa6ad8af6b00\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\f1146f774d6258b00f26aa6ad8af6b00\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4dd3fdaaff8835a6d69528f823645a30\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4dd3fdaaff8835a6d69528f823645a30\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\2a7f45898462c893c925156d163f656f\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\2a7f45898462c893c925156d163f656f\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\f1e5017d927147ee57550e382728a21c\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\f1e5017d927147ee57550e382728a21c\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\d2f3baf9da570c6bc7841b93e17ab012\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\d2f3baf9da570c6bc7841b93e17ab012\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\d600dd0990b6dcf9425752e5cf74f8fb\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\d600dd0990b6dcf9425752e5cf74f8fb\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\7af4b4f491a6596e64d50ced738265c5\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\7af4b4f491a6596e64d50ced738265c5\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\97500f0365f817c48aeef75c3624e1ff\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\97500f0365f817c48aeef75c3624e1ff\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\9aeaa20feb746deed08812d1648685c4\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\9aeaa20feb746deed08812d1648685c4\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\f26d33392aad83b4a93bce8ea0c88368\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\f26d33392aad83b4a93bce8ea0c88368\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\edea32bee283cb823eb7cb435e4a8ee9\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\edea32bee283cb823eb7cb435e4a8ee9\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\98ab0d71b60d4f922b7af62eb452e747\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\98ab0d71b60d4f922b7af62eb452e747\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\SDK\.gradle\caches\8.11.1\transforms\5858ef100c4cfcd4b2676b4bc0b4d5ed\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\SDK\.gradle\caches\8.11.1\transforms\5858ef100c4cfcd4b2676b4bc0b4d5ed\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\6ec472f0f3951334fe806e2ee6a2ef6d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\6ec472f0f3951334fe806e2ee6a2ef6d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\418d17911481cf6fb34ad14d796f81b1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\418d17911481cf6fb34ad14d796f81b1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\975c1d8adc23a6c6f997efa5b6192589\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\975c1d8adc23a6c6f997efa5b6192589\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\58b6d2d79e584f1872a1217950ffd323\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\58b6d2d79e584f1872a1217950ffd323\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\d3cf6caf4f6734eb2de3b4ee5e489228\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\d3cf6caf4f6734eb2de3b4ee5e489228\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\d3cf6caf4f6734eb2de3b4ee5e489228\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\92a51bc7c02ff55fe83543917b2f6d99\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\92a51bc7c02ff55fe83543917b2f6d99\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\418d17911481cf6fb34ad14d796f81b1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\418d17911481cf6fb34ad14d796f81b1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.example.tik555.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.tik555.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\92a51bc7c02ff55fe83543917b2f6d99\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\92a51bc7c02ff55fe83543917b2f6d99\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\92a51bc7c02ff55fe83543917b2f6d99\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4dd3fdaaff8835a6d69528f823645a30\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:23:9-26:79
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4dd3fdaaff8835a6d69528f823645a30\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4dd3fdaaff8835a6d69528f823645a30\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:26:13-76
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4dd3fdaaff8835a6d69528f823645a30\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:24:13-63
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
