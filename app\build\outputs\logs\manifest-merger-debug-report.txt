-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:2:1-34:12
INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:2:1-34:12
INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:2:1-34:12
INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:2:1-34:12
MERGED from [androidx.media3:media3-ui:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\cb4e11135627ad7155a925c218efe46e\transformed\media3-ui-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\2868c17b97d14d6689f039e8425202ae\transformed\media3-datasource-okhttp-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\ad5d918caa227d7fae6166cdc789271e\transformed\media3-datasource-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\64e2405ccbe9b1638a7989eed32dfefc\transformed\media3-database-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\e7837515eaf8b205a4f26440c730bf41\transformed\media3-extractor-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\9565c54fc4ccab02ddd7a7c9c956d957\transformed\media3-container-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\ab4cb4e63b50931e422ba77fc5e6634e\transformed\media3-decoder-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\69784e4f898bfdee0e7d19798a58d46a\transformed\media3-common-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\0fd183eed73303e0d8adf1f47d0f3fe5\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.7.0] D:\SDK\.gradle\caches\8.11.1\transforms\520e1882f2952e038a0bf5987a9655b4\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\27ef927dfa77e341cb4a2e500abfd866\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] D:\SDK\.gradle\caches\8.11.1\transforms\82da4bd88c75535472e099c38aac915b\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\ff3f81a0c1699dcfcdb03be15b69c061\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\15040066fdfc217fd7057977ebd1b5d5\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\ba59da89848837d1311cb612f22886cb\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\abbc4a94ec703e07dc13c9459494637b\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\799dcd0f174a3870708ccbaf0edc0ad1\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\c0d8fa24410bfdef3ab1eb4ecba40002\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\c7183881c118680f354402770b12a8e9\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\57f94e286ebd1ff2d023cc54a147cd34\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\5754aa6e85499ddee0f80ddab00f8b86\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\a26e34e82901515343c42083ac6c133c\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\8cf99907859de29f7fe1db5d29037b89\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.animation:animation-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\350386ea6fcf52124f9d9d48e952d778\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\136920e5ede7f04633020219b8e7a2af\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\19d069744fa00860960c2518b4a58305\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\9333a29ae740aa16e2264ceaf1f3e03b\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\1e07ad5ef2490af696c6e60846717930\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] D:\SDK\.gradle\caches\8.11.1\transforms\0be35e9e3aa9c1f5a64b15b91a98bf0f\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\2ee3eb1789cb524143b3e29ad3b0ac36\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\78819af1ef177595fe9f4cac9f1d40ce\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\63ba0f6341066b6dbcc4b3f2f43f9051\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\1bca682a78b3db0b5b8921d52e176685\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\b091e07420b080bf28ac89ea2a4c8198\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\4dd5a5e9abccc326ccc62a4537000f53\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\2943668879440495afebc0733aab5375\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\daacb66ca707ac1a86072b051fe87c70\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\60ff20f150fbe026bb05ef2c50598b84\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\2a8dfdb72895b913e55e6a842b9238c7\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\c10a1fe36c69988bf339d34f0de2f70e\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] D:\SDK\.gradle\caches\8.11.1\transforms\2eecc2bd37086bdc2b8135e637bc2bd7\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\402c9dcc315b2462c9ffa2b8bd9a3c9c\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\6e3f9d659e7350f6afb2e82b10415271\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.activity:activity:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\aaacaec1b00c591e7102e08799159a8a\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\2bc14543de6e6e928ed335f1c2702e41\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\85f356cdef8b0728383ee78227c73dad\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\1b385096fe57f7ccc36fc752fce6a43d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\03613715156aadad2a6c4a502413ca4b\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\96aad46291cd5f0c8c3457756c06aa16\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\2c195fb22b7cee5c6f12ca164ec3de3d\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\1e92b3b9f8637f249a234962a1c29575\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\c9ed8f5457893c813448203dee6e7555\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\43b799a26dd757c3d437624ef1b44369\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\SDK\.gradle\caches\8.11.1\transforms\0733106c50860a6c4b7c1f889f040408\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\53f050b2695d9085b15e4db9bb68bfe2\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\aaa01776b193ee0d30f84ec17952c180\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\486a9f50ef874dffd46f04df64607070\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\fe47b0fd3386d6f3481b8213ef6aab8c\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:6:5-79
MERGED from [androidx.media3:media3-common:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\69784e4f898bfdee0e7d19798a58d46a\transformed\media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\69784e4f898bfdee0e7d19798a58d46a\transformed\media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\0fd183eed73303e0d8adf1f47d0f3fe5\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\0fd183eed73303e0d8adf1f47d0f3fe5\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:7:5-8:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:8:9-35
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:7:22-78
application
ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:10:5-32:19
INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:10:5-32:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\8cf99907859de29f7fe1db5d29037b89\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\8cf99907859de29f7fe1db5d29037b89\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\2ee3eb1789cb524143b3e29ad3b0ac36\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\2ee3eb1789cb524143b3e29ad3b0ac36\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\6e3f9d659e7350f6afb2e82b10415271\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\6e3f9d659e7350f6afb2e82b10415271\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\43b799a26dd757c3d437624ef1b44369\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\43b799a26dd757c3d437624ef1b44369\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\aaa01776b193ee0d30f84ec17952c180\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\aaa01776b193ee0d30f84ec17952c180\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:17:9-35
	android:label
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:15:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:13:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:16:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:20:9-29
	android:icon
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:14:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:11:9-35
	android:theme
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:18:9-44
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:12:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:19:9-44
activity#com.example.tik555.MainActivity
ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:21:9-31:20
	android:label
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:24:13-45
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:23:13-36
	android:theme
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:25:13-48
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:22:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:26:13-30:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:27:17-69
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:27:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:29:17-77
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:29:27-74
uses-sdk
INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml
MERGED from [androidx.media3:media3-ui:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\cb4e11135627ad7155a925c218efe46e\transformed\media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\cb4e11135627ad7155a925c218efe46e\transformed\media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\2868c17b97d14d6689f039e8425202ae\transformed\media3-datasource-okhttp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\2868c17b97d14d6689f039e8425202ae\transformed\media3-datasource-okhttp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\ad5d918caa227d7fae6166cdc789271e\transformed\media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\ad5d918caa227d7fae6166cdc789271e\transformed\media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\64e2405ccbe9b1638a7989eed32dfefc\transformed\media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\64e2405ccbe9b1638a7989eed32dfefc\transformed\media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\e7837515eaf8b205a4f26440c730bf41\transformed\media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\e7837515eaf8b205a4f26440c730bf41\transformed\media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\9565c54fc4ccab02ddd7a7c9c956d957\transformed\media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\9565c54fc4ccab02ddd7a7c9c956d957\transformed\media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\ab4cb4e63b50931e422ba77fc5e6634e\transformed\media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\ab4cb4e63b50931e422ba77fc5e6634e\transformed\media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\69784e4f898bfdee0e7d19798a58d46a\transformed\media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\69784e4f898bfdee0e7d19798a58d46a\transformed\media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\0fd183eed73303e0d8adf1f47d0f3fe5\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\0fd183eed73303e0d8adf1f47d0f3fe5\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.7.0] D:\SDK\.gradle\caches\8.11.1\transforms\520e1882f2952e038a0bf5987a9655b4\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] D:\SDK\.gradle\caches\8.11.1\transforms\520e1882f2952e038a0bf5987a9655b4\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\27ef927dfa77e341cb4a2e500abfd866\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\27ef927dfa77e341cb4a2e500abfd866\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] D:\SDK\.gradle\caches\8.11.1\transforms\82da4bd88c75535472e099c38aac915b\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] D:\SDK\.gradle\caches\8.11.1\transforms\82da4bd88c75535472e099c38aac915b\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\ff3f81a0c1699dcfcdb03be15b69c061\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\ff3f81a0c1699dcfcdb03be15b69c061\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\15040066fdfc217fd7057977ebd1b5d5\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\15040066fdfc217fd7057977ebd1b5d5\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\ba59da89848837d1311cb612f22886cb\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\ba59da89848837d1311cb612f22886cb\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\abbc4a94ec703e07dc13c9459494637b\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\abbc4a94ec703e07dc13c9459494637b\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\799dcd0f174a3870708ccbaf0edc0ad1\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\799dcd0f174a3870708ccbaf0edc0ad1\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\c0d8fa24410bfdef3ab1eb4ecba40002\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\c0d8fa24410bfdef3ab1eb4ecba40002\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\c7183881c118680f354402770b12a8e9\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\c7183881c118680f354402770b12a8e9\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\57f94e286ebd1ff2d023cc54a147cd34\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\57f94e286ebd1ff2d023cc54a147cd34\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\5754aa6e85499ddee0f80ddab00f8b86\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\5754aa6e85499ddee0f80ddab00f8b86\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\a26e34e82901515343c42083ac6c133c\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\a26e34e82901515343c42083ac6c133c\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\8cf99907859de29f7fe1db5d29037b89\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\8cf99907859de29f7fe1db5d29037b89\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\350386ea6fcf52124f9d9d48e952d778\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\350386ea6fcf52124f9d9d48e952d778\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\136920e5ede7f04633020219b8e7a2af\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\136920e5ede7f04633020219b8e7a2af\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\19d069744fa00860960c2518b4a58305\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\19d069744fa00860960c2518b4a58305\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\9333a29ae740aa16e2264ceaf1f3e03b\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\9333a29ae740aa16e2264ceaf1f3e03b\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\1e07ad5ef2490af696c6e60846717930\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\1e07ad5ef2490af696c6e60846717930\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] D:\SDK\.gradle\caches\8.11.1\transforms\0be35e9e3aa9c1f5a64b15b91a98bf0f\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] D:\SDK\.gradle\caches\8.11.1\transforms\0be35e9e3aa9c1f5a64b15b91a98bf0f\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\2ee3eb1789cb524143b3e29ad3b0ac36\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\2ee3eb1789cb524143b3e29ad3b0ac36\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\78819af1ef177595fe9f4cac9f1d40ce\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\78819af1ef177595fe9f4cac9f1d40ce\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\63ba0f6341066b6dbcc4b3f2f43f9051\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\63ba0f6341066b6dbcc4b3f2f43f9051\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\1bca682a78b3db0b5b8921d52e176685\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\1bca682a78b3db0b5b8921d52e176685\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\b091e07420b080bf28ac89ea2a4c8198\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\b091e07420b080bf28ac89ea2a4c8198\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\4dd5a5e9abccc326ccc62a4537000f53\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\4dd5a5e9abccc326ccc62a4537000f53\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\2943668879440495afebc0733aab5375\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\2943668879440495afebc0733aab5375\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\daacb66ca707ac1a86072b051fe87c70\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\daacb66ca707ac1a86072b051fe87c70\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\60ff20f150fbe026bb05ef2c50598b84\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\60ff20f150fbe026bb05ef2c50598b84\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\2a8dfdb72895b913e55e6a842b9238c7\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\2a8dfdb72895b913e55e6a842b9238c7\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\c10a1fe36c69988bf339d34f0de2f70e\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\c10a1fe36c69988bf339d34f0de2f70e\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] D:\SDK\.gradle\caches\8.11.1\transforms\2eecc2bd37086bdc2b8135e637bc2bd7\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] D:\SDK\.gradle\caches\8.11.1\transforms\2eecc2bd37086bdc2b8135e637bc2bd7\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\402c9dcc315b2462c9ffa2b8bd9a3c9c\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\402c9dcc315b2462c9ffa2b8bd9a3c9c\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\6e3f9d659e7350f6afb2e82b10415271\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\6e3f9d659e7350f6afb2e82b10415271\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\aaacaec1b00c591e7102e08799159a8a\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\aaacaec1b00c591e7102e08799159a8a\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\2bc14543de6e6e928ed335f1c2702e41\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\2bc14543de6e6e928ed335f1c2702e41\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\85f356cdef8b0728383ee78227c73dad\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\85f356cdef8b0728383ee78227c73dad\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\1b385096fe57f7ccc36fc752fce6a43d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\1b385096fe57f7ccc36fc752fce6a43d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\03613715156aadad2a6c4a502413ca4b\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\03613715156aadad2a6c4a502413ca4b\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\96aad46291cd5f0c8c3457756c06aa16\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\96aad46291cd5f0c8c3457756c06aa16\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\2c195fb22b7cee5c6f12ca164ec3de3d\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\2c195fb22b7cee5c6f12ca164ec3de3d\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\1e92b3b9f8637f249a234962a1c29575\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\1e92b3b9f8637f249a234962a1c29575\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\c9ed8f5457893c813448203dee6e7555\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\c9ed8f5457893c813448203dee6e7555\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\43b799a26dd757c3d437624ef1b44369\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\43b799a26dd757c3d437624ef1b44369\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\SDK\.gradle\caches\8.11.1\transforms\0733106c50860a6c4b7c1f889f040408\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\SDK\.gradle\caches\8.11.1\transforms\0733106c50860a6c4b7c1f889f040408\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\53f050b2695d9085b15e4db9bb68bfe2\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\53f050b2695d9085b15e4db9bb68bfe2\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\aaa01776b193ee0d30f84ec17952c180\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\aaa01776b193ee0d30f84ec17952c180\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\486a9f50ef874dffd46f04df64607070\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\486a9f50ef874dffd46f04df64607070\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\fe47b0fd3386d6f3481b8213ef6aab8c\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\fe47b0fd3386d6f3481b8213ef6aab8c\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\8cf99907859de29f7fe1db5d29037b89\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\8cf99907859de29f7fe1db5d29037b89\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\8cf99907859de29f7fe1db5d29037b89\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\2ee3eb1789cb524143b3e29ad3b0ac36\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\2ee3eb1789cb524143b3e29ad3b0ac36\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\aaa01776b193ee0d30f84ec17952c180\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\aaa01776b193ee0d30f84ec17952c180\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.example.tik555.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.tik555.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\2ee3eb1789cb524143b3e29ad3b0ac36\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\2ee3eb1789cb524143b3e29ad3b0ac36\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\2ee3eb1789cb524143b3e29ad3b0ac36\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\6e3f9d659e7350f6afb2e82b10415271\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:23:9-26:79
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\6e3f9d659e7350f6afb2e82b10415271\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\6e3f9d659e7350f6afb2e82b10415271\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:26:13-76
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\6e3f9d659e7350f6afb2e82b10415271\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:24:13-63
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
