{"logs": [{"outputFile": "com.example.tik555.app-mergeDebugResources-2:/values-pa/values-pa.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\ff3f81a0c1699dcfcdb03be15b69c061\\transformed\\material-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "86", "endOffsets": "137"}, "to": {"startLines": "141", "startColumns": "4", "startOffsets": "12015", "endColumns": "86", "endOffsets": "12097"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\95d12178e9e01a63c029617100d83575\\transformed\\core-1.16.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,558,656,785", "endColumns": "97,101,99,100,101,97,128,100", "endOffsets": "148,250,350,451,553,651,780,881"}, "to": {"startLines": "20,21,22,23,24,25,26,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "790,888,990,1090,1191,1293,1391,12814", "endColumns": "97,101,99,100,101,97,128,100", "endOffsets": "883,985,1085,1186,1288,1386,1515,12910"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\19d069744fa00860960c2518b4a58305\\transformed\\foundation-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,215", "endColumns": "72,86,86", "endOffsets": "123,210,297"}, "to": {"startLines": "19,155,156", "startColumns": "4,4,4", "startOffsets": "717,13179,13266", "endColumns": "72,86,86", "endOffsets": "785,13261,13348"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\402c9dcc315b2462c9ffa2b8bd9a3c9c\\transformed\\ui-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,272,355,447,546,634,712,810,898,982,1061,1142,1214,1297,1372,1447,1522,1602,1668", "endColumns": "92,82,91,98,87,77,97,87,83,78,80,71,82,74,74,74,79,65,117", "endOffsets": "267,350,442,541,629,707,805,893,977,1056,1137,1209,1292,1367,1442,1517,1597,1663,1781"}, "to": {"startLines": "27,28,29,30,31,82,83,142,143,144,145,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1520,1613,1696,1788,1887,5655,5733,12102,12190,12274,12353,12434,12506,12589,12664,12739,12915,12995,13061", "endColumns": "92,82,91,98,87,77,97,87,83,78,80,71,82,74,74,74,79,65,117", "endOffsets": "1608,1691,1783,1882,1970,5728,5826,12185,12269,12348,12429,12501,12584,12659,12734,12809,12990,13056,13174"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0fd183eed73303e0d8adf1f47d0f3fe5\\transformed\\media3-exoplayer-1.4.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,191,268,334,409,475,574,670", "endColumns": "70,64,76,65,74,65,98,95,84", "endOffsets": "121,186,263,329,404,470,569,665,750"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3915,3986,4051,4128,4194,4269,4335,4434,4530", "endColumns": "70,64,76,65,74,65,98,95,84", "endOffsets": "3981,4046,4123,4189,4264,4330,4429,4525,4610"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cb4e11135627ad7155a925c218efe46e\\transformed\\media3-ui-1.4.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,487,667,756,843,926,1017,1111,1182,1245,1336,1427,1491,1554,1614,1682,1790,1907,2020,2090,2166,2237,2308,2394,2478,2544,2607,2660,2718,2766,2827,2887,2959,3021,3083,3144,3206,3271,3323,3383,3457,3531,3583", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,88,86,82,90,93,70,62,90,90,63,62,59,67,107,116,112,69,75,70,70,85,83,65,62,52,57,47,60,59,71,61,61,60,61,64,51,59,73,73,51,63", "endOffsets": "279,482,662,751,838,921,1012,1106,1177,1240,1331,1422,1486,1549,1609,1677,1785,1902,2015,2085,2161,2232,2303,2389,2473,2539,2602,2655,2713,2761,2822,2882,2954,3016,3078,3139,3201,3266,3318,3378,3452,3526,3578,3642"}, "to": {"startLines": "2,11,15,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,334,537,1975,2064,2151,2234,2325,2419,2490,2553,2644,2735,2799,2862,2922,2990,3098,3215,3328,3398,3474,3545,3616,3702,3786,3852,4615,4668,4726,4774,4835,4895,4967,5029,5091,5152,5214,5279,5331,5391,5465,5539,5591", "endLines": "10,14,18,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,88,86,82,90,93,70,62,90,90,63,62,59,67,107,116,112,69,75,70,70,85,83,65,62,52,57,47,60,59,71,61,61,60,61,64,51,59,73,73,51,63", "endOffsets": "329,532,712,2059,2146,2229,2320,2414,2485,2548,2639,2730,2794,2857,2917,2985,3093,3210,3323,3393,3469,3540,3611,3697,3781,3847,3910,4663,4721,4769,4830,4890,4962,5024,5086,5147,5209,5274,5326,5386,5460,5534,5586,5650"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\82da4bd88c75535472e099c38aac915b\\transformed\\material3-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,291,407,526,623,724,842,980,1104,1247,1332,1435,1525,1622,1734,1855,1963,2098,2235,2366,2532,2658,2773,2892,3012,3103,3199,3318,3454,3556,3659,3765,3897,4035,4146,4245,4321,4418,4519,4631,4716,4804,4903,4983,5067,5167,5266,5361,5459,5545,5646,5744,5846,5961,6041,6143", "endColumns": "116,118,115,118,96,100,117,137,123,142,84,102,89,96,111,120,107,134,136,130,165,125,114,118,119,90,95,118,135,101,102,105,131,137,110,98,75,96,100,111,84,87,98,79,83,99,98,94,97,85,100,97,101,114,79,101,95", "endOffsets": "167,286,402,521,618,719,837,975,1099,1242,1327,1430,1520,1617,1729,1850,1958,2093,2230,2361,2527,2653,2768,2887,3007,3098,3194,3313,3449,3551,3654,3760,3892,4030,4141,4240,4316,4413,4514,4626,4711,4799,4898,4978,5062,5162,5261,5356,5454,5540,5641,5739,5841,5956,6036,6138,6234"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5831,5948,6067,6183,6302,6399,6500,6618,6756,6880,7023,7108,7211,7301,7398,7510,7631,7739,7874,8011,8142,8308,8434,8549,8668,8788,8879,8975,9094,9230,9332,9435,9541,9673,9811,9922,10021,10097,10194,10295,10407,10492,10580,10679,10759,10843,10943,11042,11137,11235,11321,11422,11520,11622,11737,11817,11919", "endColumns": "116,118,115,118,96,100,117,137,123,142,84,102,89,96,111,120,107,134,136,130,165,125,114,118,119,90,95,118,135,101,102,105,131,137,110,98,75,96,100,111,84,87,98,79,83,99,98,94,97,85,100,97,101,114,79,101,95", "endOffsets": "5943,6062,6178,6297,6394,6495,6613,6751,6875,7018,7103,7206,7296,7393,7505,7626,7734,7869,8006,8137,8303,8429,8544,8663,8783,8874,8970,9089,9225,9327,9430,9536,9668,9806,9917,10016,10092,10189,10290,10402,10487,10575,10674,10754,10838,10938,11037,11132,11230,11316,11417,11515,11617,11732,11812,11914,12010"}}]}]}