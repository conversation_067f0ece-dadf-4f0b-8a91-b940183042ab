# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.0.21"
  }
  digests {
    sha256: "\363\034\305?\020Z~H\300\223h;\275T7V\035\0223\222\005\023wKG\b\005d\033\355\274\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.10"
  }
  digests {
    sha256: "\244\307M\224\326L\341\253\3457`\376\003\211\335\224\037o\305X\320\332\263^G\300\205\241\036\310\017("
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.10"
  }
  digests {
    sha256: "\254ca\277\232\321\3558,!\003\331q,G\315\354\026b2\264\220>\325\226\350\207k\006\201\311\267"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.0.21"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.16.0"
  }
  digests {
    sha256: "\027f\333\330/d\241-\3155\236\313o\025\363\3175\333Mf\322)a\247\305\033/\374dh1L"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.9.1"
  }
  digests {
    sha256: "\03649\027\353\362{\251o\344\334R\261\312\327\3752\2678\373\3065[\266\315[;0]r\022\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.16.0"
  }
  digests {
    sha256: "k\360=9\333\343tJ\314\342\'\323\266\2277L6%\252\341\002_\276\310\255\237\327\275X\274\3441"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.5.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.5.0"
  }
  digests {
    sha256: "p\263Y$\344\272\274\337\3727\320\345u\356\003\234V\242\331q#4&$\304\213`23pCA"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.5.0"
  }
  digests {
    sha256: "\223\233J\206\227d\016w\330>N\213LJ\235\030\370\023\001\230\305\226\021\224\257\215\331\333\235\307\303S"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\324\":\346\250Dvw&}\377\"\206;mi\210\312h\264\322|\246\327K\201\004k\004yBW"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.9.0"
  }
  digests {
    sha256: "7\330\233!\001\360t\254l&\t\027\332\273\030V\ad^\342\000\2520\030\307\305\275\347\016\334\361\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jspecify"
    artifactId: "jspecify"
    version: "1.0.0"
  }
  digests {
    sha256: "\037\255nk\347Uw\201\344\3237)\324\232\341\315\310\375\332o\344w\273\f\306\214\343Q\352\375\373\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.9.0"
  }
  digests {
    sha256: "I}\330M\351\362\375F2O%\371\243\257\003-:#\320\207\350z\365\025\304\261\2245\366\326\246Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose-android"
    version: "2.9.0"
  }
  digests {
    sha256: "_h\274\352\f\361\332\254S \242\375W\025r7\343\tE\317~e\206q\177\274qK5\263.o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.8.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-android"
    version: "1.8.2"
  }
  digests {
    sha256: "=\265\324\377\265M\360)\347\037\262-&\r\003\247y=\320\267\263\201\224\277\231(\232\022\211\242\330Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.8.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable-android"
    version: "1.8.2"
  }
  digests {
    sha256: "E\030\233k><O\377}j\202\000\336\'\214\336s\325\b\327\022\346\371\363\372\211a&\305\312\265\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.9.0"
  }
  digests {
    sha256: "3?\242\316m\267\n\303r\267\221\337\353\251\234\002\300\331{\032\213J\264\250MGbFm\022\262\a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\372\235u\021\250\376\r^\334S\t\227\006\326O7b\035\317\353\237,Usi\355\203d_\310z#"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose-android"
    version: "2.9.0"
  }
  digests {
    sha256: "}\177\374\f\307l\225\201A<\250\251\256?ginI\354\250\v6C\313\255\222B\207\221\037N\200"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.8.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-android"
    version: "1.8.2"
  }
  digests {
    sha256: "{.\n\377+WP\004Sn\234v4\265\030hT]Hs\344bt2\207\2105\260\247\345hQ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.10.1"
  }
  digests {
    sha256: "\363\226\365\215\275w,\006[\2076\226\267J=M\buT\263vb\200\346;I\262]\273S\253\026"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.10.1"
  }
  digests {
    sha256: "\266+R\214\221}\351\276I~\266\370\2100\031| \351\322\022g\303\221la4\222\345\356\203}M"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\3336x8qnU\206h#\306\336\355b\374\354#\374\344\207\277*\217\245e\243o\024\232\263\321\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.9.0"
  }
  digests {
    sha256: "d\371kR\307\b\225\200\1770 \342\350\372\021\363\354-\251\301W\366*\256!\242\311\323\264\204\247G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "\257B\375\204\364\345+D\334\253\257\236\317\224\022\005S\222\233\301\240Y\276\262$\n\236\301\333\356\201O"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.9.0"
  }
  digests {
    sha256: "\301\\\351r\314(\223jYj\234\322V\263/\"v\212a\325\335\201\017\245k\251ER*\016\347\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.3.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-android"
    version: "1.3.0"
  }
  digests {
    sha256: "!\vi\t\273\325\025\333\364\025\220\\\273\"\017\312\223\250\177\245F\203\217u@w\353\310\340H\205\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\360\255\336E\206ADGS\205\317J\247\340\267\376\262\177a\374\371G&e\355\230\314\227\033\006\261\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.3.0"
  }
  digests {
    sha256: "E\305\366A(@\365\r\347\277}\233\034J\214\201\352#h2Hz\0218\3132\v\214\231t\026\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-compose"
    version: "1.10.1"
  }
  digests {
    sha256: "\370\232\361\262l\314\203F48|\205|-\324\364eM7e\b\220\003\234R|/\355\a\333ja"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.0.0"
  }
  digests {
    sha256: "\311F\217V\340P\006\352\025\032BlT\225|\320y\233\213\203\245y\322\204m\322 a\363>^\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.8.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry-android"
    version: "1.8.2"
  }
  digests {
    sha256: "\211\360\a\374\346\225\354&\r\352J\267\343\373\270!jIR2\003E\236+\305\036\2113\276Q\332\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.8.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util-android"
    version: "1.8.2"
  }
  digests {
    sha256: "\360\016\217\004\343\264\206\271r,\0042L%A\223\201b\257_\376\326\177-\025\264r\317\355Yl\237"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.8.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics-android"
    version: "1.8.2"
  }
  digests {
    sha256: "\337\350y\345Z\300\255\2255\223\254\343\017\211`\320\337\312S$\360\355`z\333\225\374\246\273K\221\035"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.8.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit-android"
    version: "1.8.2"
  }
  digests {
    sha256: "Koy/\316\200\003`{\022\333\\?\271i\b\177\324\021KgY\221\323\373+\005\344,\273]\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.8.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text-android"
    version: "1.8.2"
  }
  digests {
    sha256: "\221\034@\267\314\034\373\226\260P\334Y\251\305\253b\367%p\364\255\220bg^\"\324\337bd-\251"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.4.0"
  }
  digests {
    sha256: "C?\353\323CJEfqv\307jd\363\362\005\312c5\246\265D\305\265\325\177%\243\2127RB"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview"
    version: "1.8.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview-android"
    version: "1.8.2"
  }
  digests {
    sha256: "r\241\"\233\353E\314}C\344\240\222\233G\356-\006W\2466\324\231\207\303F\025>\336\271\330\305\302"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.graphics"
    artifactId: "graphics-path"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\244\003+my\263Q\360\265\232\324\265\200\355\333\271B>\026R\367\311X\203\006\207\361\356\342\354\003"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation"
    version: "1.8.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-android"
    version: "1.8.2"
  }
  digests {
    sha256: "\226\254\3036<\022\035\373\375\334\\\253\224o~Ep\\\034\316\230BKP\342\216\337\026\325Z<\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation"
    version: "1.8.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-android"
    version: "1.8.2"
  }
  digests {
    sha256: "@z\265\352e\030\306\376\366Y\024k\360\245\aG\035\355c\305\326R\215\311Q\207\"\000\'\341\364\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core"
    version: "1.8.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core-android"
    version: "1.8.2"
  }
  digests {
    sha256: "I\257\222\001\356\257Y\r\210\236t+\264=\330\340\036\372?\276G\214\270\022\365\210\177\227\217@\002a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.8.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout-android"
    version: "1.8.2"
  }
  digests {
    sha256: "\227\324t\272\363_\350\003\240\206H\2342\337\016\263QTD\216\t\033\306T;4\377\027\252R\224w"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose"
    artifactId: "compose-bom"
    version: "2025.05.01"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3"
    version: "1.3.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3-android"
    version: "1.3.2"
  }
  digests {
    sha256: "\204vf\331\374-CH\266\354v\246\246\tO\024\373`\336c\036\344\366\231 \004\354q\244\364C6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core-android"
    version: "1.7.8"
  }
  digests {
    sha256: "3,\006\262^f,\304\027\373\b~v\270\372\245\313$\237I\222\377\2436\000\204\243\324\253\210\"\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple"
    version: "1.8.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple-android"
    version: "1.8.2"
  }
  digests {
    sha256: "\357\363\200*\266\251\304\362\255m\321U9\374\237G\343\205\337\256en\375\312\211\270qmtA@\241"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer"
    version: "1.4.1"
  }
  digests {
    sha256: "f\315\017\231 \221\221f\b#\323,\217\303)\343\203\250\247\3773\340\326vc#\2076\032\274\272p"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.6"
  }
  digests {
    sha256: "\030\004\020^\236\005\375\330\367`A;\255]\344\230\303\201\2522\237O\235\224\310Q\274\211\032\306T\306"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-common"
    version: "1.4.1"
  }
  digests {
    sha256: "\227>^{\016\317\216l\214\330%\312\263QE\270LV\002\n\367\303\354R\201^Y^\367\033\252\026"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "33.0.0-android"
  }
  digests {
    sha256: "d\0055\001J0LP\"\032\272\321\235k\"\240\226\3611\031\307\304G\246U\314\036\000\2322 \215"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.2"
  }
  digests {
    sha256: "\212\217\201\317\2335\236?m\372i\032\036wi\205\300a\357/\"<\233,\200u>\033E\216\200d"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-container"
    version: "1.4.1"
  }
  digests {
    sha256: "e\266\322,\226\337\265\316vuK\b\365\260\004\356\r\275\276`y<^\253\237\225\301\365O\225\364\260"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-database"
    version: "1.4.1"
  }
  digests {
    sha256: "!p\256dH\315I\237\305p\332\372\201\345hz\330\023\231[|+\177\366\200\032\312\252\232\210\273J"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-datasource"
    version: "1.4.1"
  }
  digests {
    sha256: "\034\v\264\036\350\212k\372\362G \273\376-)\2403\253\255!\303\016\t!\357\275\316l\037\242\210B"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-datasource-okhttp"
    version: "1.4.1"
  }
  digests {
    sha256: "\241\376\376:\310\265P\206\021\236\257\345c\037\375\252\324\222\374\345Y\240O\b\305\221^\374\256R3\372"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.6.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.6.0"
  }
  digests {
    sha256: "gT?\a6\374B*\351\'\355\016PK\230\274^&\237\332\r5\000W\2237\313q=\242\204\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-decoder"
    version: "1.4.1"
  }
  digests {
    sha256: "\367\331|Z9\334\243\303\302\033\310\247\001E\3311\330c>\320\332\350\221\230\303p\347\267\343\303\361\203"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-extractor"
    version: "1.4.1"
  }
  digests {
    sha256: "\037Q\344c>\036a7\360\244\270\005R\257\303\231\342i \213p\003R\371eC\324Q;\346\277("
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-ui"
    version: "1.4.1"
  }
  digests {
    sha256: "\352`O\032\210\376i\033\035M\253\212\343\243\336\021\a)4\026)\335i1\036\3506\021\343,\227\262"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.7.0"
  }
  digests {
    sha256: "\201\241\231\356\207\306\323\325\237\263_}\276\307\033?\035Pq(a\020\231\266\254:K\034Z\v\361\371"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.3.0"
  }
  digests {
    sha256: "\326Y(\240\017cX\232I\342\031%A.\017H\205/\211%K\a\260<\003\rV\017\221\357\374\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "2.11.0"
  }
  digests {
    sha256: "\237O\273\316pr\205\204\373\356\323\215@a\363mDw\350\233\312t\264\342\254\212\353h\031\260\376C"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "converter-gson"
    version: "2.11.0"
  }
  digests {
    sha256: "=+Kf\211\tF\204e\305wd0\265r2\']\001eQ? \374\226\260\317\344\252S\023&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.11.0"
  }
  digests {
    sha256: "W\222\215nZn\336\262\253\323w\n\217\225\272D\334\344_;#\267\251\334+0\234X\025R\247\213"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.27.0"
  }
  digests {
    sha256: "$\311#7,X\343]\v\237\026\240(\222\233\271\256\334wR\030g\302t\362\275\a5\337[\241\365"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "logging-interceptor"
    version: "4.12.0"
  }
  digests {
    sha256: "\363\350\325\360\220<%\f+U\322\364\177\317\340\b\350\00648]\2508Qa\307\246:\256\320\307L"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 8
  library_dep_index: 0
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 51
  library_dep_index: 84
  library_dep_index: 0
  library_dep_index: 27
  library_dep_index: 5
  library_dep_index: 0
}
library_dependencies {
  library_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 11
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 0
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 10
  library_dep_index: 10
}
library_dependencies {
  library_index: 13
  library_dep_index: 6
  library_dep_index: 14
}
library_dependencies {
  library_index: 15
  library_dep_index: 0
  library_dep_index: 0
}
library_dependencies {
  library_index: 16
  library_dep_index: 6
}
library_dependencies {
  library_index: 17
  library_dep_index: 18
}
library_dependencies {
  library_index: 18
  library_dep_index: 6
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 15
  library_dep_index: 21
  library_dep_index: 58
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 48
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 47
  library_dep_index: 49
}
library_dependencies {
  library_index: 19
  library_dep_index: 6
}
library_dependencies {
  library_index: 20
  library_dep_index: 6
  library_dep_index: 19
}
library_dependencies {
  library_index: 21
  library_dep_index: 22
}
library_dependencies {
  library_index: 22
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 17
  library_dep_index: 29
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 48
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 47
  library_dep_index: 49
}
library_dependencies {
  library_index: 23
  library_dep_index: 24
}
library_dependencies {
  library_index: 24
  library_dep_index: 1
  library_dep_index: 25
  library_dep_index: 4
  library_dep_index: 2
}
library_dependencies {
  library_index: 25
  library_dep_index: 26
  library_dep_index: 24
  library_dep_index: 23
}
library_dependencies {
  library_index: 26
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 2
}
library_dependencies {
  library_index: 28
  library_dep_index: 6
  library_dep_index: 21
  library_dep_index: 21
  library_dep_index: 17
  library_dep_index: 29
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 48
  library_dep_index: 45
  library_dep_index: 47
  library_dep_index: 49
}
library_dependencies {
  library_index: 29
  library_dep_index: 30
}
library_dependencies {
  library_index: 30
  library_dep_index: 6
  library_dep_index: 31
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 47
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 48
  library_dep_index: 45
  library_dep_index: 49
}
library_dependencies {
  library_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 32
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 33
  library_dep_index: 0
}
library_dependencies {
  library_index: 33
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 31
  library_dep_index: 0
  library_dep_index: 31
  library_dep_index: 0
}
library_dependencies {
  library_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 0
  library_dep_index: 48
  library_dep_index: 45
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 47
  library_dep_index: 49
}
library_dependencies {
  library_index: 37
  library_dep_index: 38
}
library_dependencies {
  library_index: 38
  library_dep_index: 6
  library_dep_index: 15
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 17
  library_dep_index: 29
  library_dep_index: 35
  library_dep_index: 39
  library_dep_index: 48
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 47
  library_dep_index: 49
}
library_dependencies {
  library_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 6
  library_dep_index: 31
  library_dep_index: 41
  library_dep_index: 21
  library_dep_index: 37
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 54
  library_dep_index: 21
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 48
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 47
  library_dep_index: 49
}
library_dependencies {
  library_index: 41
  library_dep_index: 42
}
library_dependencies {
  library_index: 42
  library_dep_index: 43
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 60
  library_dep_index: 10
  library_dep_index: 10
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 61
  library_dep_index: 65
  library_dep_index: 69
  library_dep_index: 67
  library_dep_index: 63
  library_dep_index: 8
  library_dep_index: 75
  library_dep_index: 71
  library_dep_index: 29
  library_dep_index: 37
  library_dep_index: 48
  library_dep_index: 58
  library_dep_index: 57
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 27
  library_dep_index: 61
  library_dep_index: 65
  library_dep_index: 69
  library_dep_index: 72
  library_dep_index: 67
  library_dep_index: 63
  library_dep_index: 76
  library_dep_index: 0
}
library_dependencies {
  library_index: 43
  library_dep_index: 44
  library_dep_index: 35
  library_dep_index: 48
  library_dep_index: 57
  library_dep_index: 44
  library_dep_index: 59
}
library_dependencies {
  library_index: 44
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 15
  library_dep_index: 21
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 45
  library_dep_index: 58
  library_dep_index: 52
  library_dep_index: 51
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 59
  library_dep_index: 43
  library_dep_index: 0
}
library_dependencies {
  library_index: 45
  library_dep_index: 46
}
library_dependencies {
  library_index: 46
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 47
  library_dep_index: 37
  library_dep_index: 52
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 54
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 47
  library_dep_index: 17
  library_dep_index: 29
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 48
  library_dep_index: 0
  library_dep_index: 49
}
library_dependencies {
  library_index: 47
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 0
  library_dep_index: 27
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 17
  library_dep_index: 29
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 48
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 49
}
library_dependencies {
  library_index: 48
  library_dep_index: 37
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 47
  library_dep_index: 49
}
library_dependencies {
  library_index: 49
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 47
  library_dep_index: 17
  library_dep_index: 29
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 48
  library_dep_index: 45
  library_dep_index: 0
}
library_dependencies {
  library_index: 50
  library_dep_index: 6
  library_dep_index: 51
}
library_dependencies {
  library_index: 51
  library_dep_index: 6
}
library_dependencies {
  library_index: 52
  library_dep_index: 53
}
library_dependencies {
  library_index: 53
  library_dep_index: 6
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 15
  library_dep_index: 21
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 54
  library_dep_index: 57
  library_dep_index: 0
}
library_dependencies {
  library_index: 54
  library_dep_index: 55
}
library_dependencies {
  library_index: 55
  library_dep_index: 56
  library_dep_index: 0
}
library_dependencies {
  library_index: 56
  library_dep_index: 55
  library_dep_index: 54
}
library_dependencies {
  library_index: 57
  library_dep_index: 52
  library_dep_index: 0
  library_dep_index: 52
  library_dep_index: 0
}
library_dependencies {
  library_index: 58
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 50
  library_dep_index: 14
}
library_dependencies {
  library_index: 59
  library_dep_index: 43
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 41
  library_dep_index: 5
  library_dep_index: 21
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 52
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 43
  library_dep_index: 0
  library_dep_index: 44
}
library_dependencies {
  library_index: 60
  library_dep_index: 8
}
library_dependencies {
  library_index: 61
  library_dep_index: 62
}
library_dependencies {
  library_index: 62
  library_dep_index: 6
  library_dep_index: 31
  library_dep_index: 63
  library_dep_index: 0
  library_dep_index: 41
  library_dep_index: 65
  library_dep_index: 69
  library_dep_index: 72
  library_dep_index: 67
  library_dep_index: 63
  library_dep_index: 0
}
library_dependencies {
  library_index: 63
  library_dep_index: 64
}
library_dependencies {
  library_index: 64
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 0
  library_dep_index: 41
  library_dep_index: 61
  library_dep_index: 65
  library_dep_index: 69
  library_dep_index: 72
  library_dep_index: 67
  library_dep_index: 0
}
library_dependencies {
  library_index: 65
  library_dep_index: 66
}
library_dependencies {
  library_index: 66
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 31
  library_dep_index: 67
  library_dep_index: 63
  library_dep_index: 8
  library_dep_index: 74
  library_dep_index: 0
  library_dep_index: 41
  library_dep_index: 61
  library_dep_index: 69
  library_dep_index: 72
  library_dep_index: 67
  library_dep_index: 63
  library_dep_index: 0
}
library_dependencies {
  library_index: 67
  library_dep_index: 68
}
library_dependencies {
  library_index: 68
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 31
  library_dep_index: 61
  library_dep_index: 63
  library_dep_index: 0
  library_dep_index: 41
  library_dep_index: 61
  library_dep_index: 65
  library_dep_index: 69
  library_dep_index: 72
  library_dep_index: 63
  library_dep_index: 0
}
library_dependencies {
  library_index: 69
  library_dep_index: 70
}
library_dependencies {
  library_index: 70
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 65
  library_dep_index: 67
  library_dep_index: 63
  library_dep_index: 8
  library_dep_index: 71
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 41
  library_dep_index: 61
  library_dep_index: 65
  library_dep_index: 72
  library_dep_index: 67
  library_dep_index: 63
  library_dep_index: 0
}
library_dependencies {
  library_index: 71
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 49
  library_dep_index: 50
}
library_dependencies {
  library_index: 72
  library_dep_index: 73
}
library_dependencies {
  library_index: 73
  library_dep_index: 6
  library_dep_index: 31
  library_dep_index: 4
  library_dep_index: 41
  library_dep_index: 61
  library_dep_index: 65
  library_dep_index: 69
  library_dep_index: 67
  library_dep_index: 63
  library_dep_index: 4
}
library_dependencies {
  library_index: 74
  library_dep_index: 8
  library_dep_index: 0
}
library_dependencies {
  library_index: 75
  library_dep_index: 5
  library_dep_index: 0
}
library_dependencies {
  library_index: 76
  library_dep_index: 77
}
library_dependencies {
  library_index: 77
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 78
  library_dep_index: 82
  library_dep_index: 31
  library_dep_index: 41
  library_dep_index: 69
  library_dep_index: 63
  library_dep_index: 8
  library_dep_index: 71
  library_dep_index: 0
  library_dep_index: 82
  library_dep_index: 0
}
library_dependencies {
  library_index: 78
  library_dep_index: 79
}
library_dependencies {
  library_index: 79
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 80
  library_dep_index: 82
  library_dep_index: 31
  library_dep_index: 41
  library_dep_index: 61
  library_dep_index: 65
  library_dep_index: 63
  library_dep_index: 0
  library_dep_index: 80
  library_dep_index: 0
}
library_dependencies {
  library_index: 80
  library_dep_index: 81
}
library_dependencies {
  library_index: 81
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 31
  library_dep_index: 41
  library_dep_index: 65
  library_dep_index: 67
  library_dep_index: 63
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 78
  library_dep_index: 0
}
library_dependencies {
  library_index: 82
  library_dep_index: 83
}
library_dependencies {
  library_index: 83
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 80
  library_dep_index: 31
  library_dep_index: 41
  library_dep_index: 67
  library_dep_index: 63
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 76
  library_dep_index: 0
}
library_dependencies {
  library_index: 84
  library_dep_index: 6
  library_dep_index: 10
}
library_dependencies {
  library_index: 85
  library_dep_index: 76
  library_dep_index: 86
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 41
  library_dep_index: 65
  library_dep_index: 72
  library_dep_index: 77
  library_dep_index: 87
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 42
  library_dep_index: 66
  library_dep_index: 73
  library_dep_index: 78
  library_dep_index: 82
  library_dep_index: 69
  library_dep_index: 63
  library_dep_index: 80
  library_dep_index: 88
  library_dep_index: 90
  library_dep_index: 61
  library_dep_index: 67
  library_dep_index: 83
  library_dep_index: 79
  library_dep_index: 70
  library_dep_index: 64
  library_dep_index: 81
  library_dep_index: 89
  library_dep_index: 91
  library_dep_index: 62
  library_dep_index: 68
}
library_dependencies {
  library_index: 86
  library_dep_index: 87
}
library_dependencies {
  library_index: 87
  library_dep_index: 59
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 80
  library_dep_index: 76
  library_dep_index: 82
  library_dep_index: 88
  library_dep_index: 90
  library_dep_index: 31
  library_dep_index: 41
  library_dep_index: 69
  library_dep_index: 63
  library_dep_index: 28
  library_dep_index: 4
}
library_dependencies {
  library_index: 88
  library_dep_index: 89
}
library_dependencies {
  library_index: 89
  library_dep_index: 41
  library_dep_index: 0
  library_dep_index: 4
}
library_dependencies {
  library_index: 90
  library_dep_index: 91
}
library_dependencies {
  library_index: 91
  library_dep_index: 10
  library_dep_index: 78
  library_dep_index: 76
  library_dep_index: 31
  library_dep_index: 63
  library_dep_index: 0
  library_dep_index: 0
}
library_dependencies {
  library_index: 92
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 93
  library_dep_index: 94
  library_dep_index: 97
  library_dep_index: 99
  library_dep_index: 104
  library_dep_index: 105
  library_dep_index: 98
}
library_dependencies {
  library_index: 93
  library_dep_index: 6
}
library_dependencies {
  library_index: 94
  library_dep_index: 6
  library_dep_index: 95
  library_dep_index: 9
  library_dep_index: 97
  library_dep_index: 98
  library_dep_index: 99
  library_dep_index: 100
  library_dep_index: 104
  library_dep_index: 92
  library_dep_index: 105
  library_dep_index: 106
}
library_dependencies {
  library_index: 95
  library_dep_index: 96
  library_dep_index: 14
}
library_dependencies {
  library_index: 97
  library_dep_index: 6
  library_dep_index: 94
}
library_dependencies {
  library_index: 98
  library_dep_index: 94
  library_dep_index: 6
}
library_dependencies {
  library_index: 99
  library_dep_index: 94
  library_dep_index: 98
  library_dep_index: 6
  library_dep_index: 93
}
library_dependencies {
  library_index: 100
  library_dep_index: 94
  library_dep_index: 99
  library_dep_index: 6
  library_dep_index: 101
}
library_dependencies {
  library_index: 101
  library_dep_index: 102
  library_dep_index: 2
}
library_dependencies {
  library_index: 102
  library_dep_index: 103
}
library_dependencies {
  library_index: 103
  library_dep_index: 2
  library_dep_index: 4
}
library_dependencies {
  library_index: 104
  library_dep_index: 94
  library_dep_index: 6
}
library_dependencies {
  library_index: 105
  library_dep_index: 6
  library_dep_index: 94
  library_dep_index: 97
  library_dep_index: 104
}
library_dependencies {
  library_index: 106
  library_dep_index: 94
  library_dep_index: 107
  library_dep_index: 6
  library_dep_index: 108
}
library_dependencies {
  library_index: 107
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 8
}
library_dependencies {
  library_index: 108
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 109
  library_dep_index: 75
}
library_dependencies {
  library_index: 109
  library_dep_index: 6
  library_dep_index: 8
}
library_dependencies {
  library_index: 110
  library_dep_index: 101
}
library_dependencies {
  library_index: 111
  library_dep_index: 110
  library_dep_index: 112
}
library_dependencies {
  library_index: 112
  library_dep_index: 113
}
library_dependencies {
  library_index: 114
  library_dep_index: 101
  library_dep_index: 2
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 35
  dependency_index: 59
  dependency_index: 85
  dependency_index: 41
  dependency_index: 65
  dependency_index: 72
  dependency_index: 86
  dependency_index: 92
  dependency_index: 106
  dependency_index: 100
  dependency_index: 98
  dependency_index: 110
  dependency_index: 111
  dependency_index: 101
  dependency_index: 114
  dependency_index: 112
  dependency_index: 76
  dependency_index: 39
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
