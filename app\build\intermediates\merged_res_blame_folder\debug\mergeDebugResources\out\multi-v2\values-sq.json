{"logs": [{"outputFile": "com.example.tik555.app-mergeDebugResources-2:/values-sq/values-sq.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\17d8797a605bf267136b984c4f4c6900\\transformed\\media3-ui-1.7.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,485,671,752,833,919,1023,1115,1188,1251,1341,1431,1496,1559,1626,1694,1843,1992,2135,2202,2284,2356,2429,2528,2627,2691,2761,2814,2872,2920,2981,3046,3112,3174,3242,3306,3365,3431,3483,3548,3626,3704,3761", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,80,80,85,103,91,72,62,89,89,64,62,66,67,148,148,142,66,81,71,72,98,98,63,69,52,57,47,60,64,65,61,67,63,58,65,51,64,77,77,56,68", "endOffsets": "281,480,666,747,828,914,1018,1110,1183,1246,1336,1426,1491,1554,1621,1689,1838,1987,2130,2197,2279,2351,2424,2523,2622,2686,2756,2809,2867,2915,2976,3041,3107,3169,3237,3301,3360,3426,3478,3543,3621,3699,3756,3825"}, "to": {"startLines": "2,11,15,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,535,2019,2100,2181,2267,2371,2463,2536,2599,2689,2779,2844,2907,2974,3042,3191,3340,3483,3550,3632,3704,3777,3876,3975,4039,4779,4832,4890,4938,4999,5064,5130,5192,5260,5324,5383,5449,5501,5566,5644,5722,5779", "endLines": "10,14,18,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,80,80,85,103,91,72,62,89,89,64,62,66,67,148,148,142,66,81,71,72,98,98,63,69,52,57,47,60,64,65,61,67,63,58,65,51,64,77,77,56,68", "endOffsets": "331,530,716,2095,2176,2262,2366,2458,2531,2594,2684,2774,2839,2902,2969,3037,3186,3335,3478,3545,3627,3699,3772,3871,3970,4034,4104,4827,4885,4933,4994,5059,5125,5187,5255,5319,5378,5444,5496,5561,5639,5717,5774,5843"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\dc0228f35d5d9920c8fe531ae3e0365a\\transformed\\media3-exoplayer-1.7.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,254,320,398,477,569,655", "endColumns": "70,60,66,65,77,78,91,85,69", "endOffsets": "121,182,249,315,393,472,564,650,720"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4109,4180,4241,4308,4374,4452,4531,4623,4709", "endColumns": "70,60,66,65,77,78,91,85,69", "endOffsets": "4175,4236,4303,4369,4447,4526,4618,4704,4774"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,290,409,527,629,724,836,974,1090,1236,1320,1420,1512,1611,1729,1853,1958,2095,2229,2373,2562,2700,2823,2947,3073,3166,3262,3387,3528,3623,3734,3843,3982,4127,4238,4337,4414,4508,4602,4722,4810,4893,4998,5084,5167,5266,5367,5462,5560,5648,5754,5854,5957,6085,6170,6284", "endColumns": "118,115,118,117,101,94,111,137,115,145,83,99,91,98,117,123,104,136,133,143,188,137,122,123,125,92,95,124,140,94,110,108,138,144,110,98,76,93,93,119,87,82,104,85,82,98,100,94,97,87,105,99,102,127,84,113,106", "endOffsets": "169,285,404,522,624,719,831,969,1085,1231,1315,1415,1507,1606,1724,1848,1953,2090,2224,2368,2557,2695,2818,2942,3068,3161,3257,3382,3523,3618,3729,3838,3977,4122,4233,4332,4409,4503,4597,4717,4805,4888,4993,5079,5162,5261,5362,5457,5555,5643,5749,5849,5952,6080,6165,6279,6386"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6022,6141,6257,6376,6494,6596,6691,6803,6941,7057,7203,7287,7387,7479,7578,7696,7820,7925,8062,8196,8340,8529,8667,8790,8914,9040,9133,9229,9354,9495,9590,9701,9810,9949,10094,10205,10304,10381,10475,10569,10689,10777,10860,10965,11051,11134,11233,11334,11429,11527,11615,11721,11821,11924,12052,12137,12251", "endColumns": "118,115,118,117,101,94,111,137,115,145,83,99,91,98,117,123,104,136,133,143,188,137,122,123,125,92,95,124,140,94,110,108,138,144,110,98,76,93,93,119,87,82,104,85,82,98,100,94,97,87,105,99,102,127,84,113,106", "endOffsets": "6136,6252,6371,6489,6591,6686,6798,6936,7052,7198,7282,7382,7474,7573,7691,7815,7920,8057,8191,8335,8524,8662,8785,8909,9035,9128,9224,9349,9490,9585,9696,9805,9944,10089,10200,10299,10376,10470,10564,10684,10772,10855,10960,11046,11129,11228,11329,11424,11522,11610,11716,11816,11919,12047,12132,12246,12353"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\8a2e8e7142c38a8f2ee7133e4de5b14c\\transformed\\material-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "141", "startColumns": "4", "startOffsets": "12358", "endColumns": "92", "endOffsets": "12446"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "20,21,22,23,24,25,26,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "807,906,1008,1106,1203,1311,1422,13197", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "901,1003,1101,1198,1306,1417,1539,13293"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "192,287,370,469,571,667,748,841,933,1023,1110,1201,1274,1363,1438,1514,1587,1664,1730", "endColumns": "94,82,98,101,95,80,92,91,89,86,90,72,88,74,75,72,76,65,120", "endOffsets": "282,365,464,566,662,743,836,928,1018,1105,1196,1269,1358,1433,1509,1582,1659,1725,1846"}, "to": {"startLines": "27,28,29,30,31,82,83,142,143,144,145,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1544,1639,1722,1821,1923,5848,5929,12451,12543,12633,12720,12811,12884,12973,13048,13124,13298,13375,13441", "endColumns": "94,82,98,101,95,80,92,91,89,86,90,72,88,74,75,72,76,65,120", "endOffsets": "1634,1717,1816,1918,2014,5924,6017,12538,12628,12715,12806,12879,12968,13043,13119,13192,13370,13436,13557"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,239", "endColumns": "85,97,98", "endOffsets": "136,234,333"}, "to": {"startLines": "19,155,156", "startColumns": "4,4,4", "startOffsets": "721,13562,13660", "endColumns": "85,97,98", "endOffsets": "802,13655,13754"}}]}]}