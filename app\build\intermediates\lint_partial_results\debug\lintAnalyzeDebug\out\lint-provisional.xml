<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="conditional_incidents">

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="7"
            column="36"
            startOffset="348"
            endLine="7"
            endColumn="77"
            endOffset="389"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="28"/>
            <entry
                name="read"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="838"
                endOffset="858"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="19"
            column="9"
            startOffset="838"
            endLine="19"
            endColumn="29"
            endOffset="858"/>
        <map>
            <condition minGE="31-∞"/>
        </map>
    </incident>

</incidents>
