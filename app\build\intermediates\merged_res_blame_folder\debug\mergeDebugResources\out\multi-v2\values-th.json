{"logs": [{"outputFile": "com.example.tik555.app-mergeDebugResources-2:/values-th/values-th.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,280,393,505,604,697,807,937,1061,1202,1288,1388,1479,1577,1695,1811,1916,2043,2167,2295,2447,2570,2688,2812,2933,3025,3124,3236,3369,3465,3583,3690,3816,3950,4060,4158,4239,4333,4427,4534,4620,4703,4808,4888,4975,5074,5176,5270,5374,5460,5561,5659,5762,5879,5959,6069", "endColumns": "112,111,112,111,98,92,109,129,123,140,85,99,90,97,117,115,104,126,123,127,151,122,117,123,120,91,98,111,132,95,117,106,125,133,109,97,80,93,93,106,85,82,104,79,86,98,101,93,103,85,100,97,102,116,79,109,105", "endOffsets": "163,275,388,500,599,692,802,932,1056,1197,1283,1383,1474,1572,1690,1806,1911,2038,2162,2290,2442,2565,2683,2807,2928,3020,3119,3231,3364,3460,3578,3685,3811,3945,4055,4153,4234,4328,4422,4529,4615,4698,4803,4883,4970,5069,5171,5265,5369,5455,5556,5654,5757,5874,5954,6064,6170"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5789,5902,6014,6127,6239,6338,6431,6541,6671,6795,6936,7022,7122,7213,7311,7429,7545,7650,7777,7901,8029,8181,8304,8422,8546,8667,8759,8858,8970,9103,9199,9317,9424,9550,9684,9794,9892,9973,10067,10161,10268,10354,10437,10542,10622,10709,10808,10910,11004,11108,11194,11295,11393,11496,11613,11693,11803", "endColumns": "112,111,112,111,98,92,109,129,123,140,85,99,90,97,117,115,104,126,123,127,151,122,117,123,120,91,98,111,132,95,117,106,125,133,109,97,80,93,93,106,85,82,104,79,86,98,101,93,103,85,100,97,102,116,79,109,105", "endOffsets": "5897,6009,6122,6234,6333,6426,6536,6666,6790,6931,7017,7117,7208,7306,7424,7540,7645,7772,7896,8024,8176,8299,8417,8541,8662,8754,8853,8965,9098,9194,9312,9419,9545,9679,9789,9887,9968,10062,10156,10263,10349,10432,10537,10617,10704,10803,10905,10999,11103,11189,11290,11388,11491,11608,11688,11798,11904"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\17d8797a605bf267136b984c4f4c6900\\transformed\\media3-ui-1.7.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,478,656,737,816,895,983,1073,1144,1208,1299,1390,1454,1517,1582,1653,1762,1867,1980,2048,2131,2204,2275,2360,2443,2506,2570,2623,2681,2729,2790,2849,2917,2983,3051,3114,3173,3239,3293,3356,3438,3515,3569", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,80,78,78,87,89,70,63,90,90,63,62,64,70,108,104,112,67,82,72,70,84,82,62,63,52,57,47,60,58,67,65,67,62,58,65,53,62,81,76,53,67", "endOffsets": "278,473,651,732,811,890,978,1068,1139,1203,1294,1385,1449,1512,1577,1648,1757,1862,1975,2043,2126,2199,2270,2355,2438,2501,2565,2618,2676,2724,2785,2844,2912,2978,3046,3109,3168,3234,3288,3351,3433,3510,3564,3632"}, "to": {"startLines": "2,11,15,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,333,528,1957,2038,2117,2196,2284,2374,2445,2509,2600,2691,2755,2818,2883,2954,3063,3168,3281,3349,3432,3505,3576,3661,3744,3807,4552,4605,4663,4711,4772,4831,4899,4965,5033,5096,5155,5221,5275,5338,5420,5497,5551", "endLines": "10,14,18,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,80,78,78,87,89,70,63,90,90,63,62,64,70,108,104,112,67,82,72,70,84,82,62,63,52,57,47,60,58,67,65,67,62,58,65,53,62,81,76,53,67", "endOffsets": "328,523,701,2033,2112,2191,2279,2369,2440,2504,2595,2686,2750,2813,2878,2949,3058,3163,3276,3344,3427,3500,3571,3656,3739,3802,3866,4600,4658,4706,4767,4826,4894,4960,5028,5091,5150,5216,5270,5333,5415,5492,5546,5614"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\dc0228f35d5d9920c8fe531ae3e0365a\\transformed\\media3-exoplayer-1.7.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,194,262,330,407,480,571,657", "endColumns": "75,62,67,67,76,72,90,85,78", "endOffsets": "126,189,257,325,402,475,566,652,731"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3871,3947,4010,4078,4146,4223,4296,4387,4473", "endColumns": "75,62,67,67,76,72,90,85,78", "endOffsets": "3942,4005,4073,4141,4218,4291,4382,4468,4547"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\8a2e8e7142c38a8f2ee7133e4de5b14c\\transformed\\material-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "94", "endOffsets": "145"}, "to": {"startLines": "141", "startColumns": "4", "startOffsets": "11909", "endColumns": "94", "endOffsets": "11999"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,239", "endColumns": "86,96,94", "endOffsets": "137,234,329"}, "to": {"startLines": "19,155,156", "startColumns": "4,4,4", "startOffsets": "706,13108,13205", "endColumns": "86,96,94", "endOffsets": "788,13200,13295"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "20,21,22,23,24,25,26,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "793,889,992,1090,1188,1291,1396,12739", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "884,987,1085,1183,1286,1391,1503,12835"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values-th\\values-th.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,279,356,453,554,642,727,812,898,981,1067,1156,1229,1320,1395,1471,1547,1625,1692", "endColumns": "85,76,96,100,87,84,84,85,82,85,88,72,90,74,75,75,77,66,122", "endOffsets": "274,351,448,549,637,722,807,893,976,1062,1151,1224,1315,1390,1466,1542,1620,1687,1810"}, "to": {"startLines": "27,28,29,30,31,82,83,142,143,144,145,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1508,1594,1671,1768,1869,5619,5704,12004,12090,12173,12259,12348,12421,12512,12587,12663,12840,12918,12985", "endColumns": "85,76,96,100,87,84,84,85,82,85,88,72,90,74,75,75,77,66,122", "endOffsets": "1589,1666,1763,1864,1952,5699,5784,12085,12168,12254,12343,12416,12507,12582,12658,12734,12913,12980,13103"}}]}]}