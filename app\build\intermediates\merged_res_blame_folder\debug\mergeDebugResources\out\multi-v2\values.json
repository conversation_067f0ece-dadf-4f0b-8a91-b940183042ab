{"logs": [{"outputFile": "com.example.tik555.app-mergeDebugResources-2:/values/values.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\d600dd0990b6dcf9425752e5cf74f8fb\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "313,317", "startColumns": "4,4", "startOffsets": "18066,18256", "endColumns": "53,66", "endOffsets": "18115,18318"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\367453924602fce52a1da29b574a3bf6\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "310", "startColumns": "4", "startOffsets": "17894", "endColumns": "65", "endOffsets": "17955"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\418d17911481cf6fb34ad14d796f81b1\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "355", "startColumns": "4", "startOffsets": "20507", "endColumns": "82", "endOffsets": "20585"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\dc0228f35d5d9920c8fe531ae3e0365a\\transformed\\media3-exoplayer-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "396,397,398,399,400,401,402,403,404", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "23667,23737,23799,23864,23928,24005,24070,24160,24244", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "23732,23794,23859,23923,24000,24065,24155,24239,24308"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\ab3d1566985d0eb315118d7648207d2e\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "338", "startColumns": "4", "startOffsets": "19381", "endColumns": "42", "endOffsets": "19419"}}, {"source": "C:\\Users\\<USER>\\StudioProjects\\tik555\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "43", "endOffsets": "55"}, "to": {"startLines": "356", "startColumns": "4", "startOffsets": "20590", "endColumns": "43", "endOffsets": "20629"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\ad908614da85acc9911c8dc4731a8333\\transformed\\recyclerview-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,30", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,1398"}, "to": {"startLines": "47,162,163,164,165,166,167,314,963", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2018,8170,8229,8277,8333,8408,8484,18120,51781", "endLines": "47,162,163,164,165,166,167,314,983", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "2069,8224,8272,8328,8403,8479,8551,18181,52616"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\edea32bee283cb823eb7cb435e4a8ee9\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "337", "startColumns": "4", "startOffsets": "19338", "endColumns": "42", "endOffsets": "19376"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\db34985b86d559424c1a8bc12904a68c\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "340", "startColumns": "4", "startOffsets": "19484", "endColumns": "53", "endOffsets": "19533"}}, {"source": "C:\\Users\\<USER>\\StudioProjects\\tik555\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "83", "endOffsets": "135"}, "to": {"startLines": "702", "startColumns": "4", "startOffsets": "42554", "endColumns": "82", "endOffsets": "42632"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\17d8797a605bf267136b984c4f4c6900\\transformed\\media3-ui-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,32,33,34,38,39,40,41,42,43,44,45,46,47,48,49,54,61,62,63,64,65,66,67,72,73,74,75,76,77,78,79,80,81,82,83,84,85,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,235,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,287,291,295,299,303,307,311,315,316,322,333,337,341,345,349,353,357,361,365,369,373,377,390,395,400,405,418,426,436,440,444,448,451,467,493,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,385,439,493,656,702,751,877,926,975,1034,1088,1143,1203,1262,1314,1364,1492,1557,1605,1654,1702,1759,1806,1861,1913,1967,2021,2075,2223,2461,2511,2560,2621,2681,2737,2797,2967,3027,3080,3137,3192,3248,3305,3354,3405,3460,3514,3573,3629,3684,3971,4036,4094,4143,4191,4242,4288,4345,4402,4464,4531,4603,4647,4704,4760,4823,4896,4966,5025,5082,5129,5184,5229,5278,5333,5387,5437,5488,5542,5601,5651,5709,5765,5818,5881,5946,6009,6061,6121,6185,6251,6309,6381,6442,6512,6582,6647,6712,6783,6878,6983,7086,7167,7250,7331,7420,7513,7606,7699,7784,7879,7972,8049,8141,8219,8299,8377,8463,8545,8638,8716,8807,8888,8977,9080,9181,9265,9361,9458,9553,9646,9738,9831,9924,10017,10100,10187,10282,10375,10477,10569,10650,10745,10838,10915,10959,11000,11045,11093,11137,11180,11229,11276,11320,11376,11429,11471,11518,11566,11626,11664,11714,11758,11797,11847,11899,11937,11984,12031,12072,12111,12149,12193,12241,12283,12321,12363,12417,12464,12501,12550,12592,12633,12674,12716,12759,12797,12833,12911,12989,13286,13556,13638,13720,13862,13940,14027,14112,14179,14242,14334,14426,14491,14554,14616,14687,14797,14908,15018,15085,15165,15236,15303,15388,15473,15536,15624,15688,15830,15930,15978,16121,16184,16246,16311,16382,16440,16498,16564,16616,16678,16754,16830,16884,16997,17276,17507,17717,17930,18140,18362,18578,18782,18820,19174,19961,20202,20442,20699,20952,21205,21440,21687,21926,22170,22391,22586,23258,23549,23845,24148,24814,25348,25822,26033,26233,26409,26517,27093,28038,29633", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,32,33,37,38,39,40,41,42,43,44,45,46,47,48,53,60,61,62,63,64,65,66,71,72,73,74,75,76,77,78,79,80,81,82,83,84,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,234,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,286,290,294,298,302,306,310,314,315,321,332,336,340,344,348,352,356,360,364,368,372,376,389,394,399,404,417,425,435,439,443,447,450,466,492,537,594", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,54,59,58,51,49,9,64,47,48,47,56,46,54,51,53,53,53,9,9,49,48,60,59,55,59,9,59,52,56,54,55,56,48,50,54,53,58,55,54,9,64,57,48,47,50,45,56,56,61,66,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,101,91,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,38,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,51,61,75,75,53,112,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "330,380,434,488,651,697,746,872,921,970,1029,1083,1138,1198,1257,1309,1359,1487,1552,1600,1649,1697,1754,1801,1856,1908,1962,2016,2070,2218,2456,2506,2555,2616,2676,2732,2792,2962,3022,3075,3132,3187,3243,3300,3349,3400,3455,3509,3568,3624,3679,3966,4031,4089,4138,4186,4237,4283,4340,4397,4459,4526,4598,4642,4699,4755,4818,4891,4961,5020,5077,5124,5179,5224,5273,5328,5382,5432,5483,5537,5596,5646,5704,5760,5813,5876,5941,6004,6056,6116,6180,6246,6304,6376,6437,6507,6577,6642,6707,6778,6873,6978,7081,7162,7245,7326,7415,7508,7601,7694,7779,7874,7967,8044,8136,8214,8294,8372,8458,8540,8633,8711,8802,8883,8972,9075,9176,9260,9356,9453,9548,9641,9733,9826,9919,10012,10095,10182,10277,10370,10472,10564,10645,10740,10833,10910,10954,10995,11040,11088,11132,11175,11224,11271,11315,11371,11424,11466,11513,11561,11621,11659,11709,11753,11792,11842,11894,11932,11979,12026,12067,12106,12144,12188,12236,12278,12316,12358,12412,12459,12496,12545,12587,12628,12669,12711,12754,12792,12828,12906,12984,13281,13551,13633,13715,13857,13935,14022,14107,14174,14237,14329,14421,14486,14549,14611,14682,14792,14903,15013,15080,15160,15231,15298,15383,15468,15531,15619,15683,15825,15925,15973,16116,16179,16241,16306,16377,16435,16493,16559,16611,16673,16749,16825,16879,16992,17271,17502,17712,17925,18135,18357,18573,18777,18815,19169,19956,20197,20437,20694,20947,21200,21435,21682,21921,22165,22386,22581,23253,23544,23840,24143,24809,25343,25817,26028,26228,26404,26512,27088,28033,29628,31569"}, "to": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,32,33,34,38,40,41,42,43,44,45,46,48,49,50,51,56,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,94,95,96,97,98,99,105,106,107,108,109,110,111,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,343,344,347,351,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,517,522,526,530,534,538,542,546,550,551,557,568,572,576,580,584,588,592,596,600,604,608,612,625,630,635,640,653,661,671,675,679,705,724,835,861,906", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,430,484,538,701,747,796,922,971,1020,1079,1133,1188,1248,1307,1359,1409,1537,1662,1710,1759,1807,1864,1911,1966,2074,2128,2182,2236,2384,2622,2672,2721,2782,2842,2898,2958,3128,3188,3241,3298,3353,3409,3466,3515,3566,3621,3675,3734,3790,3845,4132,4197,4255,4304,4352,4403,4782,4839,4896,4958,5025,5097,5141,6091,6147,6210,6283,6353,6412,6469,6516,6571,6616,6665,6720,6774,6824,6875,6929,6988,7038,7096,7152,7205,7268,7333,7396,7448,7508,7572,7638,7696,7768,7829,7899,7969,8034,8099,9534,9629,9734,9837,9918,10001,10082,10171,10264,10357,10450,10535,10630,10723,10800,10892,10970,11050,11128,11214,11296,11389,11467,11558,11639,11728,11831,11932,12016,12112,12209,12304,12397,12489,12582,12675,12768,12851,12938,13033,13126,13228,13320,13401,13496,13589,15976,16020,16061,16106,16154,16198,16241,16290,16337,16381,16437,16490,16532,16579,16627,16687,16725,16775,16819,16858,16908,16960,16998,17045,17092,17133,17172,17210,17254,17302,17344,17382,17424,17478,17525,17562,17611,17653,17694,17735,17777,17820,17858,19641,19719,19940,20237,21535,21617,21699,21841,21919,22006,22091,22158,22221,22313,22405,22470,22533,22595,22666,22776,22887,22997,23064,23144,23215,23282,23367,23452,23515,23603,24313,24455,24555,24603,24746,24809,24871,24936,25007,25065,25123,25189,25241,25303,25379,25455,25509,32016,32295,32526,32736,32949,33159,33381,33597,33801,33839,34193,34980,35221,35461,35718,35971,36224,36459,36706,36945,37189,37410,37605,38277,38568,38864,39167,39833,40367,40841,41052,41252,42776,43388,47300,48245,49840", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,32,33,37,38,40,41,42,43,44,45,46,48,49,50,55,62,63,64,65,66,67,68,73,74,75,76,77,78,79,80,81,82,83,84,85,86,93,94,95,96,97,98,99,105,106,107,108,109,110,111,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,343,344,350,354,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,521,525,529,533,537,541,545,549,550,556,567,571,575,579,583,587,591,595,599,603,607,611,624,629,634,639,652,660,670,674,678,682,707,739,860,905,962", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,54,59,58,51,49,9,64,47,48,47,56,46,54,51,53,53,53,9,9,49,48,60,59,55,59,9,59,52,56,54,55,56,48,50,54,53,58,55,54,9,64,57,48,47,50,45,56,56,61,66,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,101,91,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,38,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,51,61,75,75,53,112,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "375,425,479,533,696,742,791,917,966,1015,1074,1128,1183,1243,1302,1354,1404,1532,1597,1705,1754,1802,1859,1906,1961,2013,2123,2177,2231,2379,2617,2667,2716,2777,2837,2893,2953,3123,3183,3236,3293,3348,3404,3461,3510,3561,3616,3670,3729,3785,3840,4127,4192,4250,4299,4347,4398,4444,4834,4891,4953,5020,5092,5136,5193,6142,6205,6278,6348,6407,6464,6511,6566,6611,6660,6715,6769,6819,6870,6924,6983,7033,7091,7147,7200,7263,7328,7391,7443,7503,7567,7633,7691,7763,7824,7894,7964,8029,8094,8165,9624,9729,9832,9913,9996,10077,10166,10259,10352,10445,10530,10625,10718,10795,10887,10965,11045,11123,11209,11291,11384,11462,11553,11634,11723,11826,11927,12011,12107,12204,12299,12392,12484,12577,12670,12763,12846,12933,13028,13121,13223,13315,13396,13491,13584,13661,16015,16056,16101,16149,16193,16236,16285,16332,16376,16432,16485,16527,16574,16622,16682,16720,16770,16814,16853,16903,16955,16993,17040,17087,17128,17167,17205,17249,17297,17339,17377,17419,17473,17520,17557,17606,17648,17689,17730,17772,17815,17853,17889,19714,19792,20232,20502,21612,21694,21836,21914,22001,22086,22153,22216,22308,22400,22465,22528,22590,22661,22771,22882,22992,23059,23139,23210,23277,23362,23447,23510,23598,23662,24450,24550,24598,24741,24804,24866,24931,25002,25060,25118,25184,25236,25298,25374,25450,25504,25617,32290,32521,32731,32944,33154,33376,33592,33796,33834,34188,34975,35216,35456,35713,35966,36219,36454,36701,36940,37184,37405,37600,38272,38563,38859,39162,39828,40362,40836,41047,41247,41423,42879,43959,48240,49835,51776"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\1a91f0e1379b47d50c6190e7bb707fab\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "341", "startColumns": "4", "startOffsets": "19538", "endColumns": "49", "endOffsets": "19583"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\8a2e8e7142c38a8f2ee7133e4de5b14c\\transformed\\material-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}, "to": {"startLines": "485", "startColumns": "4", "startOffsets": "30175", "endColumns": "57", "endOffsets": "30228"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2a7f45898462c893c925156d163f656f\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "318,339", "startColumns": "4,4", "startOffsets": "18323,19424", "endColumns": "41,59", "endOffsets": "18360,19479"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "345,424,425,426,427,428,429,430,431,432,433,436,437,438,439,440,441,442,443,444,445,446,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,504,514", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19797,25734,25822,25908,25989,26073,26142,26207,26290,26396,26482,26602,26656,26725,26786,26855,26944,27039,27113,27210,27303,27401,27550,27641,27729,27825,27923,27987,28055,28142,28236,28303,28375,28447,28548,28657,28733,28802,28850,28916,28980,29054,29111,29168,29240,29290,29344,29415,29486,29556,29625,29683,29759,29830,29904,29990,30040,30110,31148,31863", "endLines": "345,424,425,426,427,428,429,430,431,432,435,436,437,438,439,440,441,442,443,444,445,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,513,516", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "19865,25817,25903,25984,26068,26137,26202,26285,26391,26477,26597,26651,26720,26781,26850,26939,27034,27108,27205,27298,27396,27545,27636,27724,27820,27918,27982,28050,28137,28231,28298,28370,28442,28543,28652,28728,28797,28845,28911,28975,29049,29106,29163,29235,29285,29339,29410,29481,29551,29620,29678,29754,29825,29899,29985,30035,30105,30170,31858,32011"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,101,157", "endColumns": "45,55,54", "endOffsets": "96,152,207"}, "to": {"startLines": "357,499,500", "startColumns": "4,4,4", "startOffsets": "20634,30925,30981", "endColumns": "45,55,54", "endOffsets": "20675,30976,31031"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "39,100,101,103,104,112,113,120,121,122,123,124,125,126,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,315,316,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,346,358,359,360,361,362,363,364,495,691,692,696,697,701,703,704,708,714,740,775,796,829", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1602,4449,4521,4651,4716,5198,5267,5603,5673,5741,5813,5883,5944,6018,8556,8617,8678,8740,8804,8866,8927,8995,9095,9155,9221,9294,9363,9420,9472,13666,13738,13814,13879,13938,13997,14057,14117,14177,14237,14297,14357,14417,14477,14537,14597,14656,14716,14776,14836,14896,14956,15016,15076,15136,15196,15256,15315,15375,15435,15494,15553,15612,15671,15730,18186,18221,18365,18420,18483,18538,18596,18652,18710,18771,18834,18891,18942,19000,19050,19111,19168,19234,19268,19303,19870,20680,20747,20819,20888,20957,21031,21103,30681,41796,41913,42114,42224,42425,42637,42709,42884,43087,43964,45770,46451,47133", "endLines": "39,100,101,103,104,112,113,120,121,122,123,124,125,126,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,315,316,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,346,358,359,360,361,362,363,364,495,691,695,696,700,701,703,704,713,723,774,795,828,834", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1657,4516,4604,4711,4777,5262,5325,5668,5736,5808,5878,5939,6013,6086,8612,8673,8735,8799,8861,8922,8990,9090,9150,9216,9289,9358,9415,9467,9529,13733,13809,13874,13933,13992,14052,14112,14172,14232,14292,14352,14412,14472,14532,14592,14651,14711,14771,14831,14891,14951,15011,15071,15131,15191,15251,15310,15370,15430,15489,15548,15607,15666,15725,15784,18216,18251,18415,18478,18533,18591,18647,18705,18766,18829,18886,18937,18995,19045,19106,19163,19229,19263,19298,19333,19935,20742,20814,20883,20952,21026,21098,21186,30747,41908,42109,42219,42420,42549,42704,42771,43082,43383,45765,46446,47128,47295"}}, {"source": "C:\\Users\\<USER>\\StudioProjects\\tik555\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "102,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4609,5330,5377,5424,5471,5516,5561", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "4646,5372,5419,5466,5511,5556,5598"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2452,2517,2571,2637,2738,2796,2848,2908,2970,3024,3074,3128,3174,3228,3274,3316,3356,3403,3439,3529,3641,3752", "endLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,62,65,70", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2512,2566,2632,2733,2791,2843,2903,2965,3019,3069,3123,3169,3223,3269,3311,3351,3398,3434,3524,3636,3747,4004"}, "to": {"startLines": "264,265,266,311,312,342,365,366,367,368,369,422,423,486,487,488,489,490,491,492,493,494,496,497,498,501,683,686", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15789,15863,15921,17960,18011,19588,21191,21256,21310,21376,21477,25622,25674,30233,30295,30349,30399,30453,30499,30553,30599,30641,30752,30799,30835,31036,41428,41539", "endLines": "264,265,266,311,312,342,365,366,367,368,369,422,423,486,487,488,489,490,491,492,493,494,496,497,498,503,685,690", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "15858,15916,15971,18006,18061,19636,21251,21305,21371,21472,21530,25669,25729,30290,30344,30394,30448,30494,30548,30594,30636,30676,30794,30830,30920,31143,41534,41791"}}]}]}