{"logs": [{"outputFile": "com.example.tik555.app-mergeDebugResources-2:/values/values.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\520e1882f2952e038a0bf5987a9655b4\\transformed\\media-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,350,410,476,598,659,725", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "139,210,283,345,405,471,593,654,720,787"}, "to": {"startLines": "114,115,119,320,347,701,703,704,709,711", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "5330,5419,5631,18489,19936,42469,42645,42767,43029,43224", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "5414,5485,5699,18546,19991,42530,42762,42823,43090,43286"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\95d12178e9e01a63c029617100d83575\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "39,100,101,103,104,112,113,123,124,125,126,127,128,129,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,318,319,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,351,363,364,365,366,367,368,369,500,696,697,702,705,710,713,714,718,724,750,785,806,839", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1602,4449,4521,4651,4716,5198,5267,5836,5906,5974,6046,6116,6177,6251,8789,8850,8911,8973,9037,9099,9160,9228,9328,9388,9454,9527,9596,9653,9705,13899,13971,14047,14112,14171,14230,14290,14350,14410,14470,14530,14590,14650,14710,14770,14830,14889,14949,15009,15069,15129,15189,15249,15309,15369,15429,15489,15548,15608,15668,15727,15786,15845,15904,15963,18419,18454,18660,18715,18778,18833,18891,18947,19005,19066,19129,19186,19237,19295,19345,19406,19463,19529,19563,19598,20225,21035,21102,21174,21243,21312,21386,21458,31036,42151,42268,42535,42828,43095,43374,43446,43621,43824,44701,46507,47188,47870", "endLines": "39,100,101,103,104,112,113,123,124,125,126,127,128,129,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,318,319,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,351,363,364,365,366,367,368,369,500,696,700,702,708,710,713,714,723,733,784,805,838,844", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1657,4516,4604,4711,4777,5262,5325,5901,5969,6041,6111,6172,6246,6319,8845,8906,8968,9032,9094,9155,9223,9323,9383,9449,9522,9591,9648,9700,9762,13966,14042,14107,14166,14225,14285,14345,14405,14465,14525,14585,14645,14705,14765,14825,14884,14944,15004,15064,15124,15184,15244,15304,15364,15424,15484,15543,15603,15663,15722,15781,15840,15899,15958,16017,18449,18484,18710,18773,18828,18886,18942,19000,19061,19124,19181,19232,19290,19340,19401,19458,19524,19558,19593,19628,20290,21097,21169,21238,21307,21381,21453,21541,31102,42263,42464,42640,43024,43219,43441,43508,43819,44120,46502,47183,47865,48032"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\aaacaec1b00c591e7102e08799159a8a\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "322,343", "startColumns": "4,4", "startOffsets": "18618,19719", "endColumns": "41,59", "endOffsets": "18655,19774"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\b091e07420b080bf28ac89ea2a4c8198\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "345", "startColumns": "4", "startOffsets": "19833", "endColumns": "49", "endOffsets": "19878"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\19d069744fa00860960c2518b4a58305\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,101,157", "endColumns": "45,55,54", "endOffsets": "96,152,207"}, "to": {"startLines": "362,504,505", "startColumns": "4,4,4", "startOffsets": "20989,31280,31336", "endColumns": "45,55,54", "endOffsets": "21030,31331,31386"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\27ef927dfa77e341cb4a2e500abfd866\\transformed\\recyclerview-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,30", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,1398"}, "to": {"startLines": "47,165,166,167,168,169,170,317,973", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2018,8403,8462,8510,8566,8641,8717,18353,52518", "endLines": "47,165,166,167,168,169,170,317,993", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "2069,8457,8505,8561,8636,8712,8784,18414,53353"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\a26e34e82901515343c42083ac6c133c\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "313", "startColumns": "4", "startOffsets": "18127", "endColumns": "65", "endOffsets": "18188"}}, {"source": "C:\\Users\\<USER>\\StudioProjects\\tik555\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "43", "endOffsets": "55"}, "to": {"startLines": "361", "startColumns": "4", "startOffsets": "20945", "endColumns": "43", "endOffsets": "20984"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c9ed8f5457893c813448203dee6e7555\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "341", "startColumns": "4", "startOffsets": "19633", "endColumns": "42", "endOffsets": "19671"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2943668879440495afebc0733aab5375\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "344", "startColumns": "4", "startOffsets": "19779", "endColumns": "53", "endOffsets": "19828"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cb4e11135627ad7155a925c218efe46e\\transformed\\media3-ui-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,32,33,34,38,39,40,41,42,43,44,45,46,47,48,49,54,61,62,63,64,65,66,67,72,73,74,75,76,77,78,79,80,81,82,83,84,85,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,235,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,287,291,295,299,303,307,311,315,316,322,333,337,341,345,349,353,357,361,365,369,373,377,390,395,400,405,418,426,436,440,444,448,451,467,493,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,385,439,493,656,702,751,877,926,975,1034,1088,1143,1203,1262,1314,1364,1492,1557,1605,1654,1702,1759,1806,1861,1913,1967,2021,2075,2223,2461,2511,2560,2621,2681,2737,2797,2967,3027,3080,3137,3192,3248,3305,3354,3405,3460,3514,3573,3629,3684,3971,4036,4094,4143,4191,4242,4288,4345,4402,4464,4531,4603,4647,4704,4760,4823,4896,4966,5025,5082,5129,5184,5229,5278,5333,5387,5437,5488,5542,5601,5651,5709,5765,5818,5881,5946,6009,6061,6121,6185,6251,6309,6381,6442,6512,6582,6647,6712,6783,6878,6983,7086,7167,7250,7331,7420,7513,7606,7699,7784,7879,7972,8049,8141,8219,8299,8377,8463,8545,8638,8716,8807,8888,8977,9080,9181,9265,9361,9458,9553,9646,9738,9831,9924,10017,10100,10187,10282,10375,10477,10569,10650,10745,10838,10915,10959,11000,11045,11093,11137,11180,11229,11276,11320,11376,11429,11471,11518,11566,11626,11664,11714,11758,11797,11847,11899,11937,11984,12031,12072,12111,12149,12193,12241,12283,12321,12363,12417,12464,12501,12550,12592,12633,12674,12716,12759,12797,12833,12911,12989,13286,13556,13638,13720,13862,13940,14027,14112,14179,14242,14334,14426,14491,14554,14616,14687,14797,14908,15018,15085,15165,15236,15303,15388,15473,15536,15624,15688,15830,15930,15978,16121,16184,16246,16311,16382,16440,16498,16564,16616,16678,16754,16830,16884,16997,17276,17507,17717,17930,18140,18362,18578,18782,18820,19174,19961,20202,20442,20699,20952,21205,21440,21687,21926,22170,22391,22586,23258,23549,23845,24148,24814,25348,25822,26033,26233,26409,26517,27093,28038,29633", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,32,33,37,38,39,40,41,42,43,44,45,46,47,48,53,60,61,62,63,64,65,66,71,72,73,74,75,76,77,78,79,80,81,82,83,84,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,234,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,286,290,294,298,302,306,310,314,315,321,332,336,340,344,348,352,356,360,364,368,372,376,389,394,399,404,417,425,435,439,443,447,450,466,492,537,594", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,54,59,58,51,49,9,64,47,48,47,56,46,54,51,53,53,53,9,9,49,48,60,59,55,59,9,59,52,56,54,55,56,48,50,54,53,58,55,54,9,64,57,48,47,50,45,56,56,61,66,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,101,91,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,38,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,51,61,75,75,53,112,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "330,380,434,488,651,697,746,872,921,970,1029,1083,1138,1198,1257,1309,1359,1487,1552,1600,1649,1697,1754,1801,1856,1908,1962,2016,2070,2218,2456,2506,2555,2616,2676,2732,2792,2962,3022,3075,3132,3187,3243,3300,3349,3400,3455,3509,3568,3624,3679,3966,4031,4089,4138,4186,4237,4283,4340,4397,4459,4526,4598,4642,4699,4755,4818,4891,4961,5020,5077,5124,5179,5224,5273,5328,5382,5432,5483,5537,5596,5646,5704,5760,5813,5876,5941,6004,6056,6116,6180,6246,6304,6376,6437,6507,6577,6642,6707,6778,6873,6978,7081,7162,7245,7326,7415,7508,7601,7694,7779,7874,7967,8044,8136,8214,8294,8372,8458,8540,8633,8711,8802,8883,8972,9075,9176,9260,9356,9453,9548,9641,9733,9826,9919,10012,10095,10182,10277,10370,10472,10564,10645,10740,10833,10910,10954,10995,11040,11088,11132,11175,11224,11271,11315,11371,11424,11466,11513,11561,11621,11659,11709,11753,11792,11842,11894,11932,11979,12026,12067,12106,12144,12188,12236,12278,12316,12358,12412,12459,12496,12545,12587,12628,12669,12711,12754,12792,12828,12906,12984,13281,13551,13633,13715,13857,13935,14022,14107,14174,14237,14329,14421,14486,14549,14611,14682,14792,14903,15013,15080,15160,15231,15298,15383,15468,15531,15619,15683,15825,15925,15973,16116,16179,16241,16306,16377,16435,16493,16559,16611,16673,16749,16825,16879,16992,17271,17502,17712,17925,18135,18357,18573,18777,18815,19169,19956,20197,20437,20694,20947,21200,21435,21682,21921,22165,22386,22581,23253,23544,23840,24143,24809,25343,25817,26028,26228,26404,26512,27088,28033,29628,31569"}, "to": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,32,33,34,38,40,41,42,43,44,45,46,48,49,50,51,56,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,94,95,96,97,98,99,105,106,107,108,109,110,111,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,348,349,352,356,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,522,527,531,535,539,543,547,551,555,556,562,573,577,581,585,589,593,597,601,605,609,613,617,630,635,640,645,658,666,676,680,684,715,734,845,871,916", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,430,484,538,701,747,796,922,971,1020,1079,1133,1188,1248,1307,1359,1409,1537,1662,1710,1759,1807,1864,1911,1966,2074,2128,2182,2236,2384,2622,2672,2721,2782,2842,2898,2958,3128,3188,3241,3298,3353,3409,3466,3515,3566,3621,3675,3734,3790,3845,4132,4197,4255,4304,4352,4403,4782,4839,4896,4958,5025,5097,5141,6324,6380,6443,6516,6586,6645,6702,6749,6804,6849,6898,6953,7007,7057,7108,7162,7221,7271,7329,7385,7438,7501,7566,7629,7681,7741,7805,7871,7929,8001,8062,8132,8202,8267,8332,9767,9862,9967,10070,10151,10234,10315,10404,10497,10590,10683,10768,10863,10956,11033,11125,11203,11283,11361,11447,11529,11622,11700,11791,11872,11961,12064,12165,12249,12345,12442,12537,12630,12722,12815,12908,13001,13084,13171,13266,13359,13461,13553,13634,13729,13822,16209,16253,16294,16339,16387,16431,16474,16523,16570,16614,16670,16723,16765,16812,16860,16920,16958,17008,17052,17091,17141,17193,17231,17278,17325,17366,17405,17443,17487,17535,17577,17615,17657,17711,17758,17795,17844,17886,17927,17968,18010,18053,18091,19996,20074,20295,20592,21890,21972,22054,22196,22274,22361,22446,22513,22576,22668,22760,22825,22888,22950,23021,23131,23242,23352,23419,23499,23570,23637,23722,23807,23870,23958,24668,24810,24910,24958,25101,25164,25226,25291,25362,25420,25478,25544,25596,25658,25734,25810,25864,32371,32650,32881,33091,33304,33514,33736,33952,34156,34194,34548,35335,35576,35816,36073,36326,36579,36814,37061,37300,37544,37765,37960,38632,38923,39219,39522,40188,40722,41196,41407,41607,43513,44125,48037,48982,50577", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,32,33,37,38,40,41,42,43,44,45,46,48,49,50,55,62,63,64,65,66,67,68,73,74,75,76,77,78,79,80,81,82,83,84,85,86,93,94,95,96,97,98,99,105,106,107,108,109,110,111,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,348,349,355,359,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,526,530,534,538,542,546,550,554,555,561,572,576,580,584,588,592,596,600,604,608,612,616,629,634,639,644,657,665,675,679,683,687,717,749,870,915,972", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,54,59,58,51,49,9,64,47,48,47,56,46,54,51,53,53,53,9,9,49,48,60,59,55,59,9,59,52,56,54,55,56,48,50,54,53,58,55,54,9,64,57,48,47,50,45,56,56,61,66,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,101,91,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,38,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,51,61,75,75,53,112,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "375,425,479,533,696,742,791,917,966,1015,1074,1128,1183,1243,1302,1354,1404,1532,1597,1705,1754,1802,1859,1906,1961,2013,2123,2177,2231,2379,2617,2667,2716,2777,2837,2893,2953,3123,3183,3236,3293,3348,3404,3461,3510,3561,3616,3670,3729,3785,3840,4127,4192,4250,4299,4347,4398,4444,4834,4891,4953,5020,5092,5136,5193,6375,6438,6511,6581,6640,6697,6744,6799,6844,6893,6948,7002,7052,7103,7157,7216,7266,7324,7380,7433,7496,7561,7624,7676,7736,7800,7866,7924,7996,8057,8127,8197,8262,8327,8398,9857,9962,10065,10146,10229,10310,10399,10492,10585,10678,10763,10858,10951,11028,11120,11198,11278,11356,11442,11524,11617,11695,11786,11867,11956,12059,12160,12244,12340,12437,12532,12625,12717,12810,12903,12996,13079,13166,13261,13354,13456,13548,13629,13724,13817,13894,16248,16289,16334,16382,16426,16469,16518,16565,16609,16665,16718,16760,16807,16855,16915,16953,17003,17047,17086,17136,17188,17226,17273,17320,17361,17400,17438,17482,17530,17572,17610,17652,17706,17753,17790,17839,17881,17922,17963,18005,18048,18086,18122,20069,20147,20587,20857,21967,22049,22191,22269,22356,22441,22508,22571,22663,22755,22820,22883,22945,23016,23126,23237,23347,23414,23494,23565,23632,23717,23802,23865,23953,24017,24805,24905,24953,25096,25159,25221,25286,25357,25415,25473,25539,25591,25653,25729,25805,25859,25972,32645,32876,33086,33299,33509,33731,33947,34151,34189,34543,35330,35571,35811,36068,36321,36574,36809,37056,37295,37539,37760,37955,38627,38918,39214,39517,40183,40717,41191,41402,41602,41778,43616,44696,48977,50572,52513"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\82da4bd88c75535472e099c38aac915b\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "350,429,430,431,432,433,434,435,436,437,438,441,442,443,444,445,446,447,448,449,450,451,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,509,519", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20152,26089,26177,26263,26344,26428,26497,26562,26645,26751,26837,26957,27011,27080,27141,27210,27299,27394,27468,27565,27658,27756,27905,27996,28084,28180,28278,28342,28410,28497,28591,28658,28730,28802,28903,29012,29088,29157,29205,29271,29335,29409,29466,29523,29595,29645,29699,29770,29841,29911,29980,30038,30114,30185,30259,30345,30395,30465,31503,32218", "endLines": "350,429,430,431,432,433,434,435,436,437,440,441,442,443,444,445,446,447,448,449,450,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,518,521", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "20220,26172,26258,26339,26423,26492,26557,26640,26746,26832,26952,27006,27075,27136,27205,27294,27389,27463,27560,27653,27751,27900,27991,28079,28175,28273,28337,28405,28492,28586,28653,28725,28797,28898,29007,29083,29152,29200,29266,29330,29404,29461,29518,29590,29640,29694,29765,29836,29906,29975,30033,30109,30180,30254,30340,30390,30460,30525,32213,32366"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\daacb66ca707ac1a86072b051fe87c70\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "342", "startColumns": "4", "startOffsets": "19676", "endColumns": "42", "endOffsets": "19714"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\ff3f81a0c1699dcfcdb03be15b69c061\\transformed\\material-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}, "to": {"startLines": "490", "startColumns": "4", "startOffsets": "30530", "endColumns": "57", "endOffsets": "30583"}}, {"source": "C:\\Users\\<USER>\\StudioProjects\\tik555\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "83", "endOffsets": "135"}, "to": {"startLines": "712", "startColumns": "4", "startOffsets": "43291", "endColumns": "82", "endOffsets": "43369"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\402c9dcc315b2462c9ffa2b8bd9a3c9c\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2452,2517,2571,2637,2738,2796,2848,2908,2970,3024,3074,3128,3174,3228,3274,3316,3356,3403,3439,3529,3641,3752", "endLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,62,65,70", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2512,2566,2632,2733,2791,2843,2903,2965,3019,3069,3123,3169,3223,3269,3311,3351,3398,3434,3524,3636,3747,4004"}, "to": {"startLines": "267,268,269,314,315,346,370,371,372,373,374,427,428,491,492,493,494,495,496,497,498,499,501,502,503,506,688,691", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16022,16096,16154,18193,18244,19883,21546,21611,21665,21731,21832,25977,26029,30588,30650,30704,30754,30808,30854,30908,30954,30996,31107,31154,31190,31391,41783,41894", "endLines": "267,268,269,314,315,346,370,371,372,373,374,427,428,491,492,493,494,495,496,497,498,499,501,502,503,508,690,695", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "16091,16149,16204,18239,18294,19931,21606,21660,21726,21827,21885,26024,26084,30645,30699,30749,30803,30849,30903,30949,30991,31031,31149,31185,31275,31498,41889,42146"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0fd183eed73303e0d8adf1f47d0f3fe5\\transformed\\media3-exoplayer-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "401,402,403,404,405,406,407,408,409", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "24022,24092,24154,24219,24283,24360,24425,24515,24599", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "24087,24149,24214,24278,24355,24420,24510,24594,24663"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\1b385096fe57f7ccc36fc752fce6a43d\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "316,321", "startColumns": "4,4", "startOffsets": "18299,18551", "endColumns": "53,66", "endOffsets": "18348,18613"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\aaa01776b193ee0d30f84ec17952c180\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "360", "startColumns": "4", "startOffsets": "20862", "endColumns": "82", "endOffsets": "20940"}}, {"source": "C:\\Users\\<USER>\\StudioProjects\\tik555\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "102,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4609,5490,5537,5584,5704,5749,5794", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "4646,5532,5579,5626,5744,5789,5831"}}]}]}