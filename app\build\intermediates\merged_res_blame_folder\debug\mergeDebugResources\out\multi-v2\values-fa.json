{"logs": [{"outputFile": "com.example.tik555.app-mergeDebugResources-2:/values-fa/values-fa.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\17d8797a605bf267136b984c4f4c6900\\transformed\\media3-ui-1.7.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,313,512,696,779,864,943,1036,1128,1205,1268,1360,1447,1510,1572,1633,1700,1816,1937,2057,2126,2202,2272,2344,2429,2516,2579,2653,2707,2776,2824,2885,2943,3020,3084,3148,3208,3270,3335,3387,3446,3519,3592,3645", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,82,84,78,92,91,76,62,91,86,62,61,60,66,115,120,119,68,75,69,71,84,86,62,73,53,68,47,60,57,76,63,63,59,61,64,51,58,72,72,52,64", "endOffsets": "308,507,691,774,859,938,1031,1123,1200,1263,1355,1442,1505,1567,1628,1695,1811,1932,2052,2121,2197,2267,2339,2424,2511,2574,2648,2702,2771,2819,2880,2938,3015,3079,3143,3203,3265,3330,3382,3441,3514,3587,3640,3705"}, "to": {"startLines": "2,11,15,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,363,562,1993,2076,2161,2240,2333,2425,2502,2565,2657,2744,2807,2869,2930,2997,3113,3234,3354,3423,3499,3569,3641,3726,3813,3876,4600,4654,4723,4771,4832,4890,4967,5031,5095,5155,5217,5282,5334,5393,5466,5539,5592", "endLines": "10,14,18,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,82,84,78,92,91,76,62,91,86,62,61,60,66,115,120,119,68,75,69,71,84,86,62,73,53,68,47,60,57,76,63,63,59,61,64,51,58,72,72,52,64", "endOffsets": "358,557,741,2071,2156,2235,2328,2420,2497,2560,2652,2739,2802,2864,2925,2992,3108,3229,3349,3418,3494,3564,3636,3721,3808,3871,3945,4649,4718,4766,4827,4885,4962,5026,5090,5150,5212,5277,5329,5388,5461,5534,5587,5652"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,272,351,445,543,629,711,814,899,982,1063,1145,1219,1303,1378,1452,1524,1599,1666", "endColumns": "86,78,93,97,85,81,102,84,82,80,81,73,83,74,73,71,74,66,116", "endOffsets": "267,346,440,538,624,706,809,894,977,1058,1140,1214,1298,1373,1447,1519,1594,1661,1778"}, "to": {"startLines": "27,28,29,30,31,82,83,142,143,144,145,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1549,1636,1715,1809,1907,5657,5739,12089,12174,12257,12338,12420,12494,12578,12653,12727,12900,12975,13042", "endColumns": "86,78,93,97,85,81,102,84,82,80,81,73,83,74,73,71,74,66,116", "endOffsets": "1631,1710,1804,1902,1988,5734,5837,12169,12252,12333,12415,12489,12573,12648,12722,12794,12970,13037,13154"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\8a2e8e7142c38a8f2ee7133e4de5b14c\\transformed\\material-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "141", "startColumns": "4", "startOffsets": "12001", "endColumns": "87", "endOffsets": "12084"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "20,21,22,23,24,25,26,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "825,924,1026,1125,1225,1326,1432,12799", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "919,1021,1120,1220,1321,1427,1544,12895"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,280,394,512,608,704,818,957,1073,1208,1293,1396,1488,1585,1699,1822,1930,2063,2194,2316,2481,2603,2716,2832,2949,3042,3140,3261,3393,3500,3603,3708,3839,3975,4081,4191,4271,4364,4461,4582,4668,4752,4851,4933,5017,5118,5219,5316,5416,5503,5607,5707,5810,5930,6012,6116", "endColumns": "114,109,113,117,95,95,113,138,115,134,84,102,91,96,113,122,107,132,130,121,164,121,112,115,116,92,97,120,131,106,102,104,130,135,105,109,79,92,96,120,85,83,98,81,83,100,100,96,99,86,103,99,102,119,81,103,97", "endOffsets": "165,275,389,507,603,699,813,952,1068,1203,1288,1391,1483,1580,1694,1817,1925,2058,2189,2311,2476,2598,2711,2827,2944,3037,3135,3256,3388,3495,3598,3703,3834,3970,4076,4186,4266,4359,4456,4577,4663,4747,4846,4928,5012,5113,5214,5311,5411,5498,5602,5702,5805,5925,6007,6111,6209"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5842,5957,6067,6181,6299,6395,6491,6605,6744,6860,6995,7080,7183,7275,7372,7486,7609,7717,7850,7981,8103,8268,8390,8503,8619,8736,8829,8927,9048,9180,9287,9390,9495,9626,9762,9868,9978,10058,10151,10248,10369,10455,10539,10638,10720,10804,10905,11006,11103,11203,11290,11394,11494,11597,11717,11799,11903", "endColumns": "114,109,113,117,95,95,113,138,115,134,84,102,91,96,113,122,107,132,130,121,164,121,112,115,116,92,97,120,131,106,102,104,130,135,105,109,79,92,96,120,85,83,98,81,83,100,100,96,99,86,103,99,102,119,81,103,97", "endOffsets": "5952,6062,6176,6294,6390,6486,6600,6739,6855,6990,7075,7178,7270,7367,7481,7604,7712,7845,7976,8098,8263,8385,8498,8614,8731,8824,8922,9043,9175,9282,9385,9490,9621,9757,9863,9973,10053,10146,10243,10364,10450,10534,10633,10715,10799,10900,11001,11098,11198,11285,11389,11489,11592,11712,11794,11898,11996"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\dc0228f35d5d9920c8fe531ae3e0365a\\transformed\\media3-exoplayer-1.7.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,183,250,310,388,463,552,640", "endColumns": "66,60,66,59,77,74,88,87,64", "endOffsets": "117,178,245,305,383,458,547,635,700"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3950,4017,4078,4145,4205,4283,4358,4447,4535", "endColumns": "66,60,66,59,77,74,88,87,64", "endOffsets": "4012,4073,4140,4200,4278,4353,4442,4530,4595"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,223", "endColumns": "78,88,88", "endOffsets": "129,218,307"}, "to": {"startLines": "19,155,156", "startColumns": "4,4,4", "startOffsets": "746,13159,13248", "endColumns": "78,88,88", "endOffsets": "820,13243,13332"}}]}]}