  Activity android.app  Modifier android.app.Activity  SetupStatusBar android.app.Activity  Tik555Theme android.app.Activity  VideoScreen android.app.Activity  WindowCompat android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  window android.app.Activity  Context android.content  Modifier android.content.Context  SetupStatusBar android.content.Context  Tik555Theme android.content.Context  VideoScreen android.content.Context  WindowCompat android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  
setContent android.content.Context  Modifier android.content.ContextWrapper  SetupStatusBar android.content.ContextWrapper  Tik555Theme android.content.ContextWrapper  VideoScreen android.content.ContextWrapper  WindowCompat android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  
setContent android.content.ContextWrapper  TRANSPARENT android.graphics.Color  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  View android.view  	ViewGroup android.view  Modifier  android.view.ContextThemeWrapper  SetupStatusBar  android.view.ContextThemeWrapper  Tik555Theme  android.view.ContextThemeWrapper  VideoScreen  android.view.ContextThemeWrapper  WindowCompat  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  context android.view.View  layoutParams android.view.View  LayoutParams android.view.ViewGroup  MATCH_PARENT #android.view.ViewGroup.LayoutParams  statusBarColor android.view.Window  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  SetupStatusBar #androidx.activity.ComponentActivity  Tik555Theme #androidx.activity.ComponentActivity  VideoScreen #androidx.activity.ComponentActivity  WindowCompat #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  window #androidx.activity.ComponentActivity  Modifier -androidx.activity.ComponentActivity.Companion  SetupStatusBar -androidx.activity.ComponentActivity.Companion  Tik555Theme -androidx.activity.ComponentActivity.Companion  VideoScreen -androidx.activity.ComponentActivity.Companion  WindowCompat -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  fillMaxSize -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  
background androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  aspectRatio "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  AndroidView +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  CircleShape +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  DebugInfoOverlay +androidx.compose.foundation.layout.BoxScope  DebugInfoPanel +androidx.compose.foundation.layout.BoxScope  
FontFamily +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  PlayerTestOverlay +androidx.compose.foundation.layout.BoxScope  PlayerTestPanel +androidx.compose.foundation.layout.BoxScope  
PlayerView +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  
VerticalPager +androidx.compose.foundation.layout.BoxScope  VideoPlayerView +androidx.compose.foundation.layout.BoxScope  	ViewGroup +androidx.compose.foundation.layout.BoxScope  apply +androidx.compose.foundation.layout.BoxScope  aspectRatio +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  	clickable +androidx.compose.foundation.layout.BoxScope  clip +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  	getOrNull +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  
FontFamily .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  formatCacheSize .androidx.compose.foundation.layout.ColumnScope  get .androidx.compose.foundation.layout.ColumnScope  getPlaybackStateText .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  maxOf .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  Box +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  	clickable +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  maxOf +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  
PagerScope !androidx.compose.foundation.pager  
PagerState !androidx.compose.foundation.pager  
VerticalPager !androidx.compose.foundation.pager  rememberPagerState !androidx.compose.foundation.pager  Modifier ,androidx.compose.foundation.pager.PagerScope  VideoPlayerView ,androidx.compose.foundation.pager.PagerScope  fillMaxSize ,androidx.compose.foundation.pager.PagerScope  currentPage ,androidx.compose.foundation.pager.PagerState  CircleShape !androidx.compose.foundation.shape  CornerBasedShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  ColorScheme androidx.compose.material3  
MaterialTheme androidx.compose.material3  Shapes androidx.compose.material3  Text androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  shapes (androidx.compose.material3.MaterialTheme  medium !androidx.compose.material3.Shapes  
Composable androidx.compose.runtime  DisposableEffect androidx.compose.runtime  DisposableEffectResult androidx.compose.runtime  DisposableEffectScope androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  MutableState androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
SideEffect androidx.compose.runtime  State androidx.compose.runtime  collectAsState androidx.compose.runtime  getValue androidx.compose.runtime  mutableStateOf androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  snapshotFlow androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  	onDispose .androidx.compose.runtime.DisposableEffectScope  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  	BottomEnd androidx.compose.ui.Alignment  Center androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  TopEnd androidx.compose.ui.Alignment  	BottomEnd 'androidx.compose.ui.Alignment.Companion  Center 'androidx.compose.ui.Alignment.Companion  TopEnd 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  aspectRatio androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  
background &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Color androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  Blue "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  Green "androidx.compose.ui.graphics.Color  Red "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  Yellow "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Blue ,androidx.compose.ui.graphics.Color.Companion  Green ,androidx.compose.ui.graphics.Color.Companion  Red ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  Yellow ,androidx.compose.ui.graphics.Color.Companion  LocalContext androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  GenericFontFamily androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  	Monospace (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Monospace 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  AndroidView androidx.compose.ui.viewinterop  Bundle #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  SetupStatusBar #androidx.core.app.ComponentActivity  Tik555Theme #androidx.core.app.ComponentActivity  VideoScreen #androidx.core.app.ComponentActivity  WindowCompat #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  WindowCompat androidx.core.view  WindowInsetsCompat androidx.core.view  WindowInsetsControllerCompat androidx.core.view  getInsetsController androidx.core.view.WindowCompat  setDecorFitsSystemWindows androidx.core.view.WindowCompat  navigationBars *androidx.core.view.WindowInsetsCompat.Type  %BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE /androidx.core.view.WindowInsetsControllerCompat  hide /androidx.core.view.WindowInsetsControllerCompat  isAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  systemBarsBehavior /androidx.core.view.WindowInsetsControllerCompat  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  	onCleared androidx.lifecycle.ViewModel  	viewModel $androidx.lifecycle.viewmodel.compose  	MediaItem androidx.media3.common  Player androidx.media3.common  fromUri  androidx.media3.common.MediaItem  clearMediaItems androidx.media3.common.Player  	isPlaying androidx.media3.common.Player  pause androidx.media3.common.Player  play androidx.media3.common.Player  
playWhenReady androidx.media3.common.Player  
playbackState androidx.media3.common.Player  prepare androidx.media3.common.Player  stop androidx.media3.common.Player  UnstableApi androidx.media3.common.util  StandaloneDatabaseProvider androidx.media3.database  DefaultDataSource androidx.media3.datasource  Factory ,androidx.media3.datasource.DefaultDataSource  	ExoPlayer androidx.media3.exoplayer  Builder #androidx.media3.exoplayer.ExoPlayer  clearMediaItems #androidx.media3.exoplayer.ExoPlayer  	isPlaying #androidx.media3.exoplayer.ExoPlayer  pause #androidx.media3.exoplayer.ExoPlayer  play #androidx.media3.exoplayer.ExoPlayer  
playWhenReady #androidx.media3.exoplayer.ExoPlayer  
playbackState #androidx.media3.exoplayer.ExoPlayer  prepare #androidx.media3.exoplayer.ExoPlayer  release #androidx.media3.exoplayer.ExoPlayer  setMediaSource #androidx.media3.exoplayer.ExoPlayer  stop #androidx.media3.exoplayer.ExoPlayer  build +androidx.media3.exoplayer.ExoPlayer.Builder  ProgressiveMediaSource  androidx.media3.exoplayer.source  Factory 7androidx.media3.exoplayer.source.ProgressiveMediaSource  createMediaSource ?androidx.media3.exoplayer.source.ProgressiveMediaSource.Factory  
PlayerView androidx.media3.ui  	ViewGroup androidx.media3.ui.PlayerView  apply androidx.media3.ui.PlayerView  layoutParams androidx.media3.ui.PlayerView  player androidx.media3.ui.PlayerView  
useController androidx.media3.ui.PlayerView  Bundle com.example.tik555  ComponentActivity com.example.tik555  
Composable com.example.tik555  MainActivity com.example.tik555  Modifier com.example.tik555  SetupStatusBar com.example.tik555  Suppress com.example.tik555  Tik555Theme com.example.tik555  VideoScreen com.example.tik555  WindowCompat com.example.tik555  WindowInsetsCompat com.example.tik555  WindowInsetsControllerCompat com.example.tik555  android com.example.tik555  fillMaxSize com.example.tik555  Modifier com.example.tik555.MainActivity  SetupStatusBar com.example.tik555.MainActivity  Tik555Theme com.example.tik555.MainActivity  VideoScreen com.example.tik555.MainActivity  WindowCompat com.example.tik555.MainActivity  enableEdgeToEdge com.example.tik555.MainActivity  fillMaxSize com.example.tik555.MainActivity  
setContent com.example.tik555.MainActivity  window com.example.tik555.MainActivity  Float com.example.tik555.data  Int com.example.tik555.data  List com.example.tik555.data  SerializedName com.example.tik555.data  String com.example.tik555.data  	VideoData com.example.tik555.data  	VideoItem com.example.tik555.data  
VideoResponse com.example.tik555.data  list !com.example.tik555.data.VideoData  aspectRatio !com.example.tik555.data.VideoItem  height !com.example.tik555.data.VideoItem  let !com.example.tik555.data.VideoItem  url !com.example.tik555.data.VideoItem  videoId !com.example.tik555.data.VideoItem  width !com.example.tik555.data.VideoItem  code %com.example.tik555.data.VideoResponse  data %com.example.tik555.data.VideoResponse  GET com.example.tik555.network  GsonConverterFactory com.example.tik555.network  HttpLoggingInterceptor com.example.tik555.network  Int com.example.tik555.network  
NetworkModule com.example.tik555.network  OkHttpClient com.example.tik555.network  Query com.example.tik555.network  Response com.example.tik555.network  Retrofit com.example.tik555.network  TimeUnit com.example.tik555.network  VideoApiService com.example.tik555.network  
VideoResponse com.example.tik555.network  apply com.example.tik555.network  java com.example.tik555.network  BASE_URL (com.example.tik555.network.NetworkModule  GsonConverterFactory (com.example.tik555.network.NetworkModule  HttpLoggingInterceptor (com.example.tik555.network.NetworkModule  OkHttpClient (com.example.tik555.network.NetworkModule  Retrofit (com.example.tik555.network.NetworkModule  TimeUnit (com.example.tik555.network.NetworkModule  VideoApiService (com.example.tik555.network.NetworkModule  apply (com.example.tik555.network.NetworkModule  java (com.example.tik555.network.NetworkModule  loggingInterceptor (com.example.tik555.network.NetworkModule  okHttpClient (com.example.tik555.network.NetworkModule  retrofit (com.example.tik555.network.NetworkModule  videoApiService (com.example.tik555.network.NetworkModule  getVideoList *com.example.tik555.network.VideoApiService  Any com.example.tik555.player  Boolean com.example.tik555.player  Context com.example.tik555.player  DefaultDataSource com.example.tik555.player  	ExoPlayer com.example.tik555.player  Int com.example.tik555.player  List com.example.tik555.player  MAX_PLAYERS com.example.tik555.player  Map com.example.tik555.player  	MediaItem com.example.tik555.player  
PlayerManager com.example.tik555.player  
PlayerWrapper com.example.tik555.player  ProgressiveMediaSource com.example.tik555.player  String com.example.tik555.player  UnstableApi com.example.tik555.player  	VideoItem com.example.tik555.player  filter com.example.tik555.player  find com.example.tik555.player  forEach com.example.tik555.player  kotlin com.example.tik555.player  let com.example.tik555.player  listOf com.example.tik555.player  map com.example.tik555.player  mapOf com.example.tik555.player  maxByOrNull com.example.tik555.player  
mutableListOf com.example.tik555.player  mutableMapOf com.example.tik555.player  println com.example.tik555.player  set com.example.tik555.player  to com.example.tik555.player  Any 'com.example.tik555.player.PlayerManager  Boolean 'com.example.tik555.player.PlayerManager  Context 'com.example.tik555.player.PlayerManager  DefaultDataSource 'com.example.tik555.player.PlayerManager  	ExoPlayer 'com.example.tik555.player.PlayerManager  Int 'com.example.tik555.player.PlayerManager  List 'com.example.tik555.player.PlayerManager  MAX_PLAYERS 'com.example.tik555.player.PlayerManager  Map 'com.example.tik555.player.PlayerManager  	MediaItem 'com.example.tik555.player.PlayerManager  
PlayerWrapper 'com.example.tik555.player.PlayerManager  ProgressiveMediaSource 'com.example.tik555.player.PlayerManager  String 'com.example.tik555.player.PlayerManager  	VideoItem 'com.example.tik555.player.PlayerManager  activePlayerMap 'com.example.tik555.player.PlayerManager  bindVideoToPlayer 'com.example.tik555.player.PlayerManager  context 'com.example.tik555.player.PlayerManager  createNewPlayer 'com.example.tik555.player.PlayerManager  dataSourceFactory 'com.example.tik555.player.PlayerManager  filter 'com.example.tik555.player.PlayerManager  find 'com.example.tik555.player.PlayerManager  findFarthestPlayer 'com.example.tik555.player.PlayerManager  getPlayerForVideo 'com.example.tik555.player.PlayerManager  
getPlayerInfo 'com.example.tik555.player.PlayerManager  kotlin 'com.example.tik555.player.PlayerManager  let 'com.example.tik555.player.PlayerManager  listOf 'com.example.tik555.player.PlayerManager  map 'com.example.tik555.player.PlayerManager  mapOf 'com.example.tik555.player.PlayerManager  maxByOrNull 'com.example.tik555.player.PlayerManager  
mutableListOf 'com.example.tik555.player.PlayerManager  mutableMapOf 'com.example.tik555.player.PlayerManager  
pauseVideo 'com.example.tik555.player.PlayerManager  	playVideo 'com.example.tik555.player.PlayerManager  
playerPool 'com.example.tik555.player.PlayerManager  
preloadVideos 'com.example.tik555.player.PlayerManager  println 'com.example.tik555.player.PlayerManager  release 'com.example.tik555.player.PlayerManager  removePlayer 'com.example.tik555.player.PlayerManager  set 'com.example.tik555.player.PlayerManager  to 'com.example.tik555.player.PlayerManager  DefaultDataSource 1com.example.tik555.player.PlayerManager.Companion  	ExoPlayer 1com.example.tik555.player.PlayerManager.Companion  MAX_PLAYERS 1com.example.tik555.player.PlayerManager.Companion  	MediaItem 1com.example.tik555.player.PlayerManager.Companion  
PlayerWrapper 1com.example.tik555.player.PlayerManager.Companion  ProgressiveMediaSource 1com.example.tik555.player.PlayerManager.Companion  filter 1com.example.tik555.player.PlayerManager.Companion  find 1com.example.tik555.player.PlayerManager.Companion  kotlin 1com.example.tik555.player.PlayerManager.Companion  let 1com.example.tik555.player.PlayerManager.Companion  listOf 1com.example.tik555.player.PlayerManager.Companion  map 1com.example.tik555.player.PlayerManager.Companion  mapOf 1com.example.tik555.player.PlayerManager.Companion  maxByOrNull 1com.example.tik555.player.PlayerManager.Companion  
mutableListOf 1com.example.tik555.player.PlayerManager.Companion  mutableMapOf 1com.example.tik555.player.PlayerManager.Companion  println 1com.example.tik555.player.PlayerManager.Companion  set 1com.example.tik555.player.PlayerManager.Companion  to 1com.example.tik555.player.PlayerManager.Companion  hashCode 5com.example.tik555.player.PlayerManager.PlayerWrapper  isPreloaded 5com.example.tik555.player.PlayerManager.PlayerWrapper  let 5com.example.tik555.player.PlayerManager.PlayerWrapper  player 5com.example.tik555.player.PlayerManager.PlayerWrapper  
videoIndex 5com.example.tik555.player.PlayerManager.PlayerWrapper  	videoItem 5com.example.tik555.player.PlayerManager.PlayerWrapper  Dispatchers com.example.tik555.repository  	Exception com.example.tik555.repository  List com.example.tik555.repository  
NetworkModule com.example.tik555.repository  Result com.example.tik555.repository  	VideoItem com.example.tik555.repository  VideoRepository com.example.tik555.repository  
apiService com.example.tik555.repository  failure com.example.tik555.repository  success com.example.tik555.repository  withContext com.example.tik555.repository  Dispatchers -com.example.tik555.repository.VideoRepository  	Exception -com.example.tik555.repository.VideoRepository  
NetworkModule -com.example.tik555.repository.VideoRepository  Result -com.example.tik555.repository.VideoRepository  
apiService -com.example.tik555.repository.VideoRepository  failure -com.example.tik555.repository.VideoRepository  getVideoList -com.example.tik555.repository.VideoRepository  success -com.example.tik555.repository.VideoRepository  withContext -com.example.tik555.repository.VideoRepository  	Alignment  com.example.tik555.ui.components  AndroidView  com.example.tik555.ui.components  Any  com.example.tik555.ui.components  Arrangement  com.example.tik555.ui.components  Boolean  com.example.tik555.ui.components  Box  com.example.tik555.ui.components  CircleShape  com.example.tik555.ui.components  Color  com.example.tik555.ui.components  
Composable  com.example.tik555.ui.components  DebugInfoOverlay  com.example.tik555.ui.components  DebugInfoPanel  com.example.tik555.ui.components  
FontFamily  com.example.tik555.ui.components  Int  com.example.tik555.ui.components  List  com.example.tik555.ui.components  Long  com.example.tik555.ui.components  Map  com.example.tik555.ui.components  
MaterialTheme  com.example.tik555.ui.components  Modifier  com.example.tik555.ui.components  
PlayerManager  com.example.tik555.ui.components  PlayerTestOverlay  com.example.tik555.ui.components  PlayerTestPanel  com.example.tik555.ui.components  
PlayerView  com.example.tik555.ui.components  Row  com.example.tik555.ui.components  String  com.example.tik555.ui.components  Text  com.example.tik555.ui.components  	VideoItem  com.example.tik555.ui.components  VideoPlayerView  com.example.tik555.ui.components  	ViewGroup  com.example.tik555.ui.components  apply  com.example.tik555.ui.components  aspectRatio  com.example.tik555.ui.components  
background  com.example.tik555.ui.components  	clickable  com.example.tik555.ui.components  clip  com.example.tik555.ui.components  fillMaxSize  com.example.tik555.ui.components  fillMaxWidth  com.example.tik555.ui.components  forEach  com.example.tik555.ui.components  format  com.example.tik555.ui.components  formatCacheSize  com.example.tik555.ui.components  get  com.example.tik555.ui.components  getPlaybackStateText  com.example.tik555.ui.components  let  com.example.tik555.ui.components  maxOf  com.example.tik555.ui.components  padding  com.example.tik555.ui.components  provideDelegate  com.example.tik555.ui.components  size  com.example.tik555.ui.components  spacedBy  com.example.tik555.ui.components  
Composable com.example.tik555.ui.screens  DebugInfoOverlay com.example.tik555.ui.screens  Modifier com.example.tik555.ui.screens  PlayerTestOverlay com.example.tik555.ui.screens  Unit com.example.tik555.ui.screens  
VerticalPager com.example.tik555.ui.screens  VideoPlayerView com.example.tik555.ui.screens  VideoScreen com.example.tik555.ui.screens  VideoViewModel com.example.tik555.ui.screens  distinctUntilChanged com.example.tik555.ui.screens  fillMaxSize com.example.tik555.ui.screens  	getOrNull com.example.tik555.ui.screens  
isNotEmpty com.example.tik555.ui.screens  provideDelegate com.example.tik555.ui.screens  snapshotFlow com.example.tik555.ui.screens  Boolean com.example.tik555.ui.theme  Build com.example.tik555.ui.theme  
Composable com.example.tik555.ui.theme  DarkColorScheme com.example.tik555.ui.theme  
FontFamily com.example.tik555.ui.theme  
FontWeight com.example.tik555.ui.theme  LightColorScheme com.example.tik555.ui.theme  Pink40 com.example.tik555.ui.theme  Pink80 com.example.tik555.ui.theme  Purple40 com.example.tik555.ui.theme  Purple80 com.example.tik555.ui.theme  PurpleGrey40 com.example.tik555.ui.theme  PurpleGrey80 com.example.tik555.ui.theme  Tik555Theme com.example.tik555.ui.theme  
Typography com.example.tik555.ui.theme  Unit com.example.tik555.ui.theme  Any com.example.tik555.viewmodel  Boolean com.example.tik555.viewmodel  Int com.example.tik555.viewmodel  List com.example.tik555.viewmodel  Map com.example.tik555.viewmodel  MutableStateFlow com.example.tik555.viewmodel  
PlayerManager com.example.tik555.viewmodel  	StateFlow com.example.tik555.viewmodel  String com.example.tik555.viewmodel  	VideoItem com.example.tik555.viewmodel  VideoRepository com.example.tik555.viewmodel  VideoViewModel com.example.tik555.viewmodel  	ViewModel com.example.tik555.viewmodel  
_errorMessage com.example.tik555.viewmodel  
_isLoading com.example.tik555.viewmodel  
_videoList com.example.tik555.viewmodel  asStateFlow com.example.tik555.viewmodel  	emptyList com.example.tik555.viewmodel  emptyMap com.example.tik555.viewmodel  fold com.example.tik555.viewmodel  launch com.example.tik555.viewmodel  let com.example.tik555.viewmodel  
repository com.example.tik555.viewmodel  
toMutableList com.example.tik555.viewmodel  MutableStateFlow +com.example.tik555.viewmodel.VideoViewModel  VideoRepository +com.example.tik555.viewmodel.VideoViewModel  _currentPage +com.example.tik555.viewmodel.VideoViewModel  
_errorMessage +com.example.tik555.viewmodel.VideoViewModel  
_isLoading +com.example.tik555.viewmodel.VideoViewModel  _playerInfo +com.example.tik555.viewmodel.VideoViewModel  
_videoList +com.example.tik555.viewmodel.VideoViewModel  asStateFlow +com.example.tik555.viewmodel.VideoViewModel  	emptyList +com.example.tik555.viewmodel.VideoViewModel  emptyMap +com.example.tik555.viewmodel.VideoViewModel  fold +com.example.tik555.viewmodel.VideoViewModel  invoke +com.example.tik555.viewmodel.VideoViewModel  	isLoading +com.example.tik555.viewmodel.VideoViewModel  launch +com.example.tik555.viewmodel.VideoViewModel  let +com.example.tik555.viewmodel.VideoViewModel  loadInitialVideos +com.example.tik555.viewmodel.VideoViewModel  loadMoreVideos +com.example.tik555.viewmodel.VideoViewModel  
playerInfo +com.example.tik555.viewmodel.VideoViewModel  
playerManager +com.example.tik555.viewmodel.VideoViewModel  
preloadVideos +com.example.tik555.viewmodel.VideoViewModel  
repository +com.example.tik555.viewmodel.VideoViewModel  setPlayerManager +com.example.tik555.viewmodel.VideoViewModel  
toMutableList +com.example.tik555.viewmodel.VideoViewModel  updateCurrentPage +com.example.tik555.viewmodel.VideoViewModel  updatePlayerInfo +com.example.tik555.viewmodel.VideoViewModel  	videoList +com.example.tik555.viewmodel.VideoViewModel  viewModelScope +com.example.tik555.viewmodel.VideoViewModel  SerializedName com.google.gson.annotations  File java.io  Class 	java.lang  	Exception 	java.lang  TimeUnit java.util.concurrent  SECONDS java.util.concurrent.TimeUnit  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  Nothing kotlin  Pair kotlin  Result kotlin  Suppress kotlin  	Throwable kotlin  apply kotlin  fold kotlin  let kotlin  map kotlin  to kotlin  not kotlin.Boolean  sp 
kotlin.Double  	compareTo kotlin.Float  div kotlin.Float  	compareTo 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  div kotlin.Long  times kotlin.Long  	Companion 
kotlin.Result  failure 
kotlin.Result  fold 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  format 
kotlin.String  to 
kotlin.String  message kotlin.Throwable  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  filter kotlin.collections  find kotlin.collections  fold kotlin.collections  forEach kotlin.collections  get kotlin.collections  	getOrNull kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  map kotlin.collections  mapOf kotlin.collections  maxByOrNull kotlin.collections  maxOf kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  set kotlin.collections  
toMutableList kotlin.collections  filter kotlin.collections.List  get kotlin.collections.List  	getOrNull kotlin.collections.List  
isNotEmpty kotlin.collections.List  maxByOrNull kotlin.collections.List  size kotlin.collections.List  
toMutableList kotlin.collections.List  Entry kotlin.collections.Map  get kotlin.collections.Map  add kotlin.collections.MutableList  addAll kotlin.collections.MutableList  clear kotlin.collections.MutableList  filter kotlin.collections.MutableList  find kotlin.collections.MutableList  map kotlin.collections.MutableList  size kotlin.collections.MutableList  clear kotlin.collections.MutableMap  containsKey kotlin.collections.MutableMap  get kotlin.collections.MutableMap  remove kotlin.collections.MutableMap  set kotlin.collections.MutableMap  size kotlin.collections.MutableMap  maxOf kotlin.comparisons  SuspendFunction1 kotlin.coroutines  println 	kotlin.io  java 
kotlin.jvm  kotlin 
kotlin.jvm  abs kotlin.math  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  fold kotlin.sequences  forEach kotlin.sequences  map kotlin.sequences  maxByOrNull kotlin.sequences  maxOf kotlin.sequences  
toMutableList kotlin.sequences  
MatchGroup kotlin.text  filter kotlin.text  find kotlin.text  fold kotlin.text  forEach kotlin.text  format kotlin.text  get kotlin.text  	getOrNull kotlin.text  
isNotEmpty kotlin.text  map kotlin.text  maxByOrNull kotlin.text  maxOf kotlin.text  set kotlin.text  
toMutableList kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  	Exception !kotlinx.coroutines.CoroutineScope  Result !kotlinx.coroutines.CoroutineScope  
_errorMessage !kotlinx.coroutines.CoroutineScope  
_isLoading !kotlinx.coroutines.CoroutineScope  
_videoList !kotlinx.coroutines.CoroutineScope  
apiService !kotlinx.coroutines.CoroutineScope  distinctUntilChanged !kotlinx.coroutines.CoroutineScope  failure !kotlinx.coroutines.CoroutineScope  fold !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  
repository !kotlinx.coroutines.CoroutineScope  snapshotFlow !kotlinx.coroutines.CoroutineScope  success !kotlinx.coroutines.CoroutineScope  
toMutableList !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  distinctUntilChanged kotlinx.coroutines.flow  collect kotlinx.coroutines.flow.Flow  distinctUntilChanged kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  OkHttpClient okhttp3  Builder okhttp3.OkHttpClient  	Companion okhttp3.OkHttpClient  addInterceptor okhttp3.OkHttpClient.Builder  build okhttp3.OkHttpClient.Builder  connectTimeout okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  writeTimeout okhttp3.OkHttpClient.Builder  HttpLoggingInterceptor okhttp3.logging  HttpLoggingInterceptor &okhttp3.logging.HttpLoggingInterceptor  Level &okhttp3.logging.HttpLoggingInterceptor  apply &okhttp3.logging.HttpLoggingInterceptor  level &okhttp3.logging.HttpLoggingInterceptor  BODY ,okhttp3.logging.HttpLoggingInterceptor.Level  Response 	retrofit2  Retrofit 	retrofit2  body retrofit2.Response  code retrofit2.Response  isSuccessful retrofit2.Response  Builder retrofit2.Retrofit  create retrofit2.Retrofit  addConverterFactory retrofit2.Retrofit.Builder  baseUrl retrofit2.Retrofit.Builder  build retrofit2.Retrofit.Builder  client retrofit2.Retrofit.Builder  GsonConverterFactory retrofit2.converter.gson  create -retrofit2.converter.gson.GsonConverterFactory  GET retrofit2.http  Query retrofit2.http                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   