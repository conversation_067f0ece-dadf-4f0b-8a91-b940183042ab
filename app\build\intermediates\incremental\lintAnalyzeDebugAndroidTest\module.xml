<lint-module
    format="1"
    dir="C:\Users\<USER>\StudioProjects\tik555\app"
    name=":app"
    type="APP"
    maven="tik555:app:unspecified"
    agpVersion="8.10.1"
    buildFolder="build"
    bootClassPath="D:\SDK\sdk\platforms\android-35\android.jar;D:\SDK\sdk\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="debug"/>
</lint-module>
