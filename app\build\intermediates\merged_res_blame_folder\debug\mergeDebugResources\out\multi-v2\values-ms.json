{"logs": [{"outputFile": "com.example.tik555.app-mergeDebugResources-2:/values-ms/values-ms.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "184,274,358,454,556,641,724,819,906,991,1076,1162,1234,1321,1398,1471,1544,1620,1686", "endColumns": "89,83,95,101,84,82,94,86,84,84,85,71,86,76,72,72,75,65,119", "endOffsets": "269,353,449,551,636,719,814,901,986,1071,1157,1229,1316,1393,1466,1539,1615,1681,1801"}, "to": {"startLines": "27,28,29,30,31,82,83,142,143,144,145,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1514,1604,1688,1784,1886,5627,5710,12199,12286,12371,12456,12542,12614,12701,12778,12851,13025,13101,13167", "endColumns": "89,83,95,101,84,82,94,86,84,84,85,71,86,76,72,72,75,65,119", "endOffsets": "1599,1683,1779,1881,1966,5705,5800,12281,12366,12451,12537,12609,12696,12773,12846,12919,13096,13162,13282"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\17d8797a605bf267136b984c4f4c6900\\transformed\\media3-ui-1.7.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,471,643,726,810,887,978,1071,1144,1213,1309,1403,1467,1530,1595,1668,1774,1883,1988,2055,2137,2207,2278,2362,2447,2514,2577,2630,2688,2736,2797,2861,2923,2984,3050,3113,3172,3238,3290,3352,3428,3504,3566", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,105,108,104,66,81,69,70,83,84,66,62,52,57,47,60,63,61,60,65,62,58,65,51,61,75,75,61,73", "endOffsets": "279,466,638,721,805,882,973,1066,1139,1208,1304,1398,1462,1525,1590,1663,1769,1878,1983,2050,2132,2202,2273,2357,2442,2509,2572,2625,2683,2731,2792,2856,2918,2979,3045,3108,3167,3233,3285,3347,3423,3499,3561,3635"}, "to": {"startLines": "2,11,15,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,334,521,1971,2054,2138,2215,2306,2399,2472,2541,2637,2731,2795,2858,2923,2996,3102,3211,3316,3383,3465,3535,3606,3690,3775,3842,4564,4617,4675,4723,4784,4848,4910,4971,5037,5100,5159,5225,5277,5339,5415,5491,5553", "endLines": "10,14,18,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,105,108,104,66,81,69,70,83,84,66,62,52,57,47,60,63,61,60,65,62,58,65,51,61,75,75,61,73", "endOffsets": "329,516,688,2049,2133,2210,2301,2394,2467,2536,2632,2726,2790,2853,2918,2991,3097,3206,3311,3378,3460,3530,3601,3685,3770,3837,3900,4612,4670,4718,4779,4843,4905,4966,5032,5095,5154,5220,5272,5334,5410,5486,5548,5622"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "20,21,22,23,24,25,26,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "771,866,968,1065,1175,1281,1399,12924", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "861,963,1060,1170,1276,1394,1509,13020"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\8a2e8e7142c38a8f2ee7133e4de5b14c\\transformed\\material-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "141", "startColumns": "4", "startOffsets": "12108", "endColumns": "90", "endOffsets": "12194"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\dc0228f35d5d9920c8fe531ae3e0365a\\transformed\\media3-exoplayer-1.7.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,189,255,320,398,464,554,637", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "120,184,250,315,393,459,549,632,709"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3905,3975,4039,4105,4170,4248,4314,4404,4487", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "3970,4034,4100,4165,4243,4309,4399,4482,4559"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,220", "endColumns": "77,86,90", "endOffsets": "128,215,306"}, "to": {"startLines": "19,155,156", "startColumns": "4,4,4", "startOffsets": "693,13287,13374", "endColumns": "77,86,90", "endOffsets": "766,13369,13460"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,294,408,527,627,732,854,1004,1132,1280,1366,1466,1558,1656,1772,1898,2003,2141,2276,2408,2587,2712,2837,2965,3094,3187,3288,3409,3537,3638,3745,3851,3992,4138,4245,4344,4420,4518,4616,4718,4805,4894,4996,5076,5159,5258,5357,5454,5557,5644,5747,5846,5953,6075,6156,6262", "endColumns": "119,118,113,118,99,104,121,149,127,147,85,99,91,97,115,125,104,137,134,131,178,124,124,127,128,92,100,120,127,100,106,105,140,145,106,98,75,97,97,101,86,88,101,79,82,98,98,96,102,86,102,98,106,121,80,105,95", "endOffsets": "170,289,403,522,622,727,849,999,1127,1275,1361,1461,1553,1651,1767,1893,1998,2136,2271,2403,2582,2707,2832,2960,3089,3182,3283,3404,3532,3633,3740,3846,3987,4133,4240,4339,4415,4513,4611,4713,4800,4889,4991,5071,5154,5253,5352,5449,5552,5639,5742,5841,5948,6070,6151,6257,6353"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5805,5925,6044,6158,6277,6377,6482,6604,6754,6882,7030,7116,7216,7308,7406,7522,7648,7753,7891,8026,8158,8337,8462,8587,8715,8844,8937,9038,9159,9287,9388,9495,9601,9742,9888,9995,10094,10170,10268,10366,10468,10555,10644,10746,10826,10909,11008,11107,11204,11307,11394,11497,11596,11703,11825,11906,12012", "endColumns": "119,118,113,118,99,104,121,149,127,147,85,99,91,97,115,125,104,137,134,131,178,124,124,127,128,92,100,120,127,100,106,105,140,145,106,98,75,97,97,101,86,88,101,79,82,98,98,96,102,86,102,98,106,121,80,105,95", "endOffsets": "5920,6039,6153,6272,6372,6477,6599,6749,6877,7025,7111,7211,7303,7401,7517,7643,7748,7886,8021,8153,8332,8457,8582,8710,8839,8932,9033,9154,9282,9383,9490,9596,9737,9883,9990,10089,10165,10263,10361,10463,10550,10639,10741,10821,10904,11003,11102,11199,11302,11389,11492,11591,11698,11820,11901,12007,12103"}}]}]}