{"logs": [{"outputFile": "com.example.tik555.app-mergeReleaseResources-2:/values-en-rIN/values-en-rIN.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,400,514,613,708,820,956,1072,1208,1292,1391,1482,1579,1698,1823,1927,2054,2177,2305,2466,2587,2703,2826,2951,3043,3141,3258,3382,3479,3581,3683,3813,3952,4058,4157,4235,4331,4425,4530,4617,4704,4806,4888,4972,5073,5174,5274,5373,5461,5567,5668,5772,5892,5974,6074", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,160,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,77,95,93,104,86,86,101,81,83,100,100,99,98,87,105,100,103,119,81,99,94", "endOffsets": "168,284,395,509,608,703,815,951,1067,1203,1287,1386,1477,1574,1693,1818,1922,2049,2172,2300,2461,2582,2698,2821,2946,3038,3136,3253,3377,3474,3576,3678,3808,3947,4053,4152,4230,4326,4420,4525,4612,4699,4801,4883,4967,5068,5169,5269,5368,5456,5562,5663,5767,5887,5969,6069,6164"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5725,5843,5959,6070,6184,6283,6378,6490,6626,6742,6878,6962,7061,7152,7249,7368,7493,7597,7724,7847,7975,8136,8257,8373,8496,8621,8713,8811,8928,9052,9149,9251,9353,9483,9622,9728,9827,9905,10001,10095,10200,10287,10374,10476,10558,10642,10743,10844,10944,11043,11131,11237,11338,11442,11562,11644,11744", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,160,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,77,95,93,104,86,86,101,81,83,100,100,99,98,87,105,100,103,119,81,99,94", "endOffsets": "5838,5954,6065,6179,6278,6373,6485,6621,6737,6873,6957,7056,7147,7244,7363,7488,7592,7719,7842,7970,8131,8252,8368,8491,8616,8708,8806,8923,9047,9144,9246,9348,9478,9617,9723,9822,9900,9996,10090,10195,10282,10369,10471,10553,10637,10738,10839,10939,11038,11126,11232,11333,11437,11557,11639,11739,11834"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "181,273,355,449,548,635,717,806,895,979,1057,1139,1212,1296,1372,1444,1514,1591,1657", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,83,75,71,69,76,65,120", "endOffsets": "268,350,444,543,630,712,801,890,974,1052,1134,1207,1291,1367,1439,1509,1586,1652,1773"}, "to": {"startLines": "27,28,29,30,31,82,83,141,142,143,144,145,146,147,148,149,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1500,1592,1674,1768,1867,5554,5636,11839,11928,12012,12090,12172,12245,12329,12405,12477,12648,12725,12791", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,83,75,71,69,76,65,120", "endOffsets": "1587,1669,1763,1862,1949,5631,5720,11923,12007,12085,12167,12240,12324,12400,12472,12542,12720,12786,12907"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\e874b47f582ea1f96bd82c21770ae83f\\transformed\\media3-ui-1.4.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,479,656,738,820,898,985,1070,1137,1200,1292,1384,1449,1512,1574,1645,1755,1866,1976,2043,2123,2194,2261,2346,2431,2494,2558,2611,2669,2717,2778,2843,2905,2970,3041,3099,3157,3223,3275,3337,3413,3489,3543", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,63,52,57,47,60,64,61,64,70,57,57,65,51,61,75,75,53,65", "endOffsets": "280,474,651,733,815,893,980,1065,1132,1195,1287,1379,1444,1507,1569,1640,1750,1861,1971,2038,2118,2189,2256,2341,2426,2489,2553,2606,2664,2712,2773,2838,2900,2965,3036,3094,3152,3218,3270,3332,3408,3484,3538,3604"}, "to": {"startLines": "2,11,15,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,529,1954,2036,2118,2196,2283,2368,2435,2498,2590,2682,2747,2810,2872,2943,3053,3164,3274,3341,3421,3492,3559,3644,3729,3792,4503,4556,4614,4662,4723,4788,4850,4915,4986,5044,5102,5168,5220,5282,5358,5434,5488", "endLines": "10,14,18,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,63,52,57,47,60,64,61,64,70,57,57,65,51,61,75,75,53,65", "endOffsets": "330,524,701,2031,2113,2191,2278,2363,2430,2493,2585,2677,2742,2805,2867,2938,3048,3159,3269,3336,3416,3487,3554,3639,3724,3787,3851,4551,4609,4657,4718,4783,4845,4910,4981,5039,5097,5163,5215,5277,5353,5429,5483,5549"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,216", "endColumns": "74,85,84", "endOffsets": "125,211,296"}, "to": {"startLines": "19,154,155", "startColumns": "4,4,4", "startOffsets": "706,12912,12998", "endColumns": "74,85,84", "endOffsets": "776,12993,13078"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "20,21,22,23,24,25,26,150", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "781,877,979,1078,1177,1281,1384,12547", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "872,974,1073,1172,1276,1379,1495,12643"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\457147f8f67deaf2a3ec4b5417c706c3\\transformed\\media3-exoplayer-1.4.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,633", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "120,182,247,311,388,453,543,628,697"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3856,3926,3988,4053,4117,4194,4259,4349,4434", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "3921,3983,4048,4112,4189,4254,4344,4429,4498"}}]}]}