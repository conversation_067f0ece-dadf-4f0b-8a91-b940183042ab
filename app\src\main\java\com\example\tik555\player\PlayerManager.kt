package com.example.tik555.player

import android.content.Context
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.database.StandaloneDatabaseProvider
// import androidx.media3.datasource.cache.CacheDataSource
// import androidx.media3.datasource.cache.LeastRecentlyUsedCacheEvictor
// import androidx.media3.datasource.cache.SimpleCache
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import com.example.tik555.data.VideoItem
import java.io.File

/**
 * 播放器管理器
 * 管理最多3个播放器实例，实现播放器复用和缓存
 */
@UnstableApi
class PlayerManager(private val context: Context) {
    
    companion object {
        private const val MAX_CACHE_SIZE = 300L * 1024 * 1024 // 300MB
        private const val MAX_PLAYERS = 3
    }
    
    // 数据源工厂（暂时不使用缓存）
    private val dataSourceFactory = DefaultDataSource.Factory(context)
    
    // 播放器池
    private val playerPool = mutableListOf<PlayerWrapper>()
    private val activePlayerMap = mutableMapOf<Int, PlayerWrapper>() // videoIndex -> PlayerWrapper
    
    /**
     * 播放器包装类
     */
    data class PlayerWrapper(
        val player: ExoPlayer,
        var videoIndex: Int = -1,
        var videoItem: VideoItem? = null,
        var isPreloaded: Boolean = false
    )
    
    /**
     * 获取或创建播放器
     */
    fun getPlayerForVideo(videoIndex: Int, videoItem: VideoItem): PlayerWrapper {
        // 检查是否已有播放器绑定到这个视频
        activePlayerMap[videoIndex]?.let { return it }
        
        // 尝试从池中获取空闲播放器
        val availablePlayer = playerPool.find { it.videoIndex == -1 }
        
        val playerWrapper = if (availablePlayer != null) {
            availablePlayer
        } else {
            // 如果池已满，移除最远的播放器
            if (playerPool.size >= MAX_PLAYERS) {
                val toRemove = findFarthestPlayer(videoIndex)
                toRemove?.let { removePlayer(it) }
            }
            
            // 创建新播放器
            createNewPlayer()
        }
        
        // 绑定视频
        bindVideoToPlayer(playerWrapper, videoIndex, videoItem)
        activePlayerMap[videoIndex] = playerWrapper
        
        return playerWrapper
    }
    
    /**
     * 创建新播放器
     */
    private fun createNewPlayer(): PlayerWrapper {
        val player = ExoPlayer.Builder(context).build()
        val wrapper = PlayerWrapper(player)
        playerPool.add(wrapper)
        println("🎬 创建新播放器: ${wrapper.hashCode()}, 总数: ${playerPool.size}")
        return wrapper
    }
    
    /**
     * 绑定视频到播放器
     */
    private fun bindVideoToPlayer(playerWrapper: PlayerWrapper, videoIndex: Int, videoItem: VideoItem) {
        playerWrapper.videoIndex = videoIndex
        playerWrapper.videoItem = videoItem
        playerWrapper.isPreloaded = false
        
        // 创建媒体源（暂时不使用缓存）
        val mediaItem = MediaItem.fromUri(videoItem.url)
        val mediaSource = ProgressiveMediaSource.Factory(dataSourceFactory)
            .createMediaSource(mediaItem)
        
        // 设置媒体源并准备
        playerWrapper.player.setMediaSource(mediaSource)
        playerWrapper.player.prepare()
        
        // 预加载但不播放
        playerWrapper.player.playWhenReady = false

        println("📹 绑定视频到播放器: 视频${videoIndex} -> 播放器${playerWrapper.hashCode()}")
    }
    
    /**
     * 查找距离当前视频最远的播放器
     */
    private fun findFarthestPlayer(currentIndex: Int): PlayerWrapper? {
        return playerPool
            .filter { it.videoIndex != -1 }
            .maxByOrNull { kotlin.math.abs(it.videoIndex - currentIndex) }
    }
    
    /**
     * 移除播放器
     */
    private fun removePlayer(playerWrapper: PlayerWrapper) {
        // 从活跃映射中移除
        activePlayerMap.remove(playerWrapper.videoIndex)

        // 停止播放器
        playerWrapper.player.stop()
        playerWrapper.player.clearMediaItems()

        // 重置状态
        playerWrapper.videoIndex = -1
        playerWrapper.videoItem = null
        playerWrapper.isPreloaded = false

        println("🗑️ 销毁播放器: ${playerWrapper.hashCode()}")
    }
    
    /**
     * 播放指定视频
     */
    fun playVideo(videoIndex: Int) {
        // 暂停所有其他播放器
        playerPool.forEach { wrapper ->
            if (wrapper.videoIndex != videoIndex) {
                wrapper.player.pause()
            }
        }
        
        // 播放指定视频
        activePlayerMap[videoIndex]?.let { wrapper ->
            wrapper.player.playWhenReady = true
            wrapper.player.play()
        }
    }
    
    /**
     * 暂停指定视频
     */
    fun pauseVideo(videoIndex: Int) {
        activePlayerMap[videoIndex]?.player?.pause()
    }
    
    /**
     * 预加载视频
     */
    fun preloadVideos(currentIndex: Int, videoList: List<VideoItem>) {
        val indicesToPreload = listOf(
            currentIndex - 1,
            currentIndex,
            currentIndex + 1
        ).filter { it >= 0 && it < videoList.size }
        
        indicesToPreload.forEach { index ->
            if (!activePlayerMap.containsKey(index)) {
                val videoItem = videoList[index]
                getPlayerForVideo(index, videoItem)
            }
        }
    }
    
    /**
     * 获取播放器状态信息
     */
    fun getPlayerInfo(): Map<String, Any> {
        return mapOf(
            "totalPlayers" to playerPool.size,
            "activePlayers" to activePlayerMap.size,
            "cacheSize" to 0L, // 暂时不显示缓存大小
            "playerStates" to playerPool.map { wrapper ->
                mapOf(
                    "videoIndex" to wrapper.videoIndex,
                    "videoId" to (wrapper.videoItem?.videoId ?: ""),
                    "isPreloaded" to wrapper.isPreloaded,
                    "playbackState" to wrapper.player.playbackState,
                    "isPlaying" to wrapper.player.isPlaying
                )
            }
        )
    }
    
    /**
     * 释放所有资源
     */
    fun release() {
        playerPool.forEach { it.player.release() }
        playerPool.clear()
        activePlayerMap.clear()
        // cache.release() // 暂时不使用缓存
    }
}
