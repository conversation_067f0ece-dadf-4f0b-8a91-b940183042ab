1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.tik555"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission
13-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:7:5-8:38
14        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
14-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:7:22-78
15        android:maxSdkVersion="28" />
15-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:8:9-35
16
17    <permission
17-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
18        android:name="com.example.tik555.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
18-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
19        android:protectionLevel="signature" />
19-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
20
21    <uses-permission android:name="com.example.tik555.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
21-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
21-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
22
23    <application
23-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:10:5-32:19
24        android:allowBackup="true"
24-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:11:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\95d12178e9e01a63c029617100d83575\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
26        android:dataExtractionRules="@xml/data_extraction_rules"
26-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:12:9-65
27        android:debuggable="true"
28        android:extractNativeLibs="false"
29        android:fullBackupContent="@xml/backup_rules"
29-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:13:9-54
30        android:icon="@mipmap/ic_launcher"
30-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:14:9-43
31        android:label="@string/app_name"
31-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:15:9-41
32        android:roundIcon="@mipmap/ic_launcher_round"
32-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:16:9-54
33        android:supportsRtl="true"
33-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:17:9-35
34        android:testOnly="true"
35        android:theme="@style/Theme.Tik555"
35-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:18:9-44
36        android:usesCleartextTraffic="true" >
36-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:19:9-44
37        <activity
37-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:21:9-31:20
38            android:name="com.example.tik555.MainActivity"
38-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:22:13-41
39            android:exported="true"
39-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:23:13-36
40            android:label="@string/app_name"
40-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:24:13-45
41            android:theme="@style/Theme.Tik555" >
41-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:25:13-48
42            <intent-filter>
42-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:26:13-30:29
43                <action android:name="android.intent.action.MAIN" />
43-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:27:17-69
43-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:27:25-66
44
45                <category android:name="android.intent.category.LAUNCHER" />
45-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:29:17-77
45-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:29:27-74
46            </intent-filter>
47        </activity>
48        <activity
48-->[androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\8cf99907859de29f7fe1db5d29037b89\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
49            android:name="androidx.compose.ui.tooling.PreviewActivity"
49-->[androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\8cf99907859de29f7fe1db5d29037b89\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
50            android:exported="true" />
50-->[androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\8cf99907859de29f7fe1db5d29037b89\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
51
52        <provider
52-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
53            android:name="androidx.startup.InitializationProvider"
53-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
54            android:authorities="com.example.tik555.androidx-startup"
54-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
55            android:exported="false" >
55-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
56            <meta-data
56-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
57                android:name="androidx.emoji2.text.EmojiCompatInitializer"
57-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
58                android:value="androidx.startup" />
58-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\33661d26dff153891c58a07eccd3c936\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
59            <meta-data
59-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\2ee3eb1789cb524143b3e29ad3b0ac36\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
60                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
60-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\2ee3eb1789cb524143b3e29ad3b0ac36\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
61                android:value="androidx.startup" />
61-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\2ee3eb1789cb524143b3e29ad3b0ac36\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
62            <meta-data
62-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
63                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
63-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
64                android:value="androidx.startup" />
64-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
65        </provider>
66
67        <activity
67-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\6e3f9d659e7350f6afb2e82b10415271\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:23:9-26:79
68            android:name="androidx.activity.ComponentActivity"
68-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\6e3f9d659e7350f6afb2e82b10415271\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:24:13-63
69            android:exported="true"
69-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\6e3f9d659e7350f6afb2e82b10415271\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:25:13-36
70            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
70-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\6e3f9d659e7350f6afb2e82b10415271\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:26:13-76
71
72        <receiver
72-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
73            android:name="androidx.profileinstaller.ProfileInstallReceiver"
73-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
74            android:directBootAware="false"
74-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
75            android:enabled="true"
75-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
76            android:exported="true"
76-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
77            android:permission="android.permission.DUMP" >
77-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
79                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
79-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
79-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
80            </intent-filter>
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
82                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
82-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
82-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
83            </intent-filter>
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
85                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
85-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
85-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
86            </intent-filter>
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
88                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
88-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
88-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\ea6d179f80f348f84ab29a039708d389\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
89            </intent-filter>
90        </receiver>
91    </application>
92
93</manifest>
