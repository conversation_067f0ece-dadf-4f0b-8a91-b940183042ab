1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.tik555"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission
13-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:7:5-8:38
14        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
14-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:7:22-78
15        android:maxSdkVersion="28" />
15-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:8:9-35
16
17    <permission
17-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
18        android:name="com.example.tik555.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
18-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
19        android:protectionLevel="signature" />
19-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
20
21    <uses-permission android:name="com.example.tik555.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
21-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
21-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
22
23    <application
23-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:10:5-31:19
24        android:allowBackup="true"
24-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:11:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\c6763e4a76f69a31c0c315f3b843ead4\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
26        android:dataExtractionRules="@xml/data_extraction_rules"
26-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:12:9-65
27        android:debuggable="true"
28        android:extractNativeLibs="false"
29        android:fullBackupContent="@xml/backup_rules"
29-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:13:9-54
30        android:icon="@mipmap/ic_launcher"
30-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:14:9-43
31        android:label="@string/app_name"
31-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:15:9-41
32        android:roundIcon="@mipmap/ic_launcher_round"
32-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:16:9-54
33        android:supportsRtl="true"
33-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:17:9-35
34        android:testOnly="true"
35        android:theme="@style/Theme.Tik555" >
35-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:18:9-44
36        <activity
36-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:20:9-30:20
37            android:name="com.example.tik555.MainActivity"
37-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:21:13-41
38            android:exported="true"
38-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:22:13-36
39            android:label="@string/app_name"
39-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:23:13-45
40            android:theme="@style/Theme.Tik555" >
40-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:24:13-48
41            <intent-filter>
41-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:25:13-29:29
42                <action android:name="android.intent.action.MAIN" />
42-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:26:17-69
42-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:26:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:28:17-77
44-->C:\Users\<USER>\StudioProjects\tik555\app\src\main\AndroidManifest.xml:28:27-74
45            </intent-filter>
46        </activity>
47        <activity
47-->[androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\d3cf6caf4f6734eb2de3b4ee5e489228\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
48            android:name="androidx.compose.ui.tooling.PreviewActivity"
48-->[androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\d3cf6caf4f6734eb2de3b4ee5e489228\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
49            android:exported="true" />
49-->[androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\d3cf6caf4f6734eb2de3b4ee5e489228\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
50
51        <provider
51-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
52            android:name="androidx.startup.InitializationProvider"
52-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
53            android:authorities="com.example.tik555.androidx-startup"
53-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
54            android:exported="false" >
54-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
55            <meta-data
55-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.emoji2.text.EmojiCompatInitializer"
56-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
57                android:value="androidx.startup" />
57-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\d1c8cdfffb481ea2f75892a019b46961\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
58            <meta-data
58-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\92a51bc7c02ff55fe83543917b2f6d99\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
59-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\92a51bc7c02ff55fe83543917b2f6d99\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
60                android:value="androidx.startup" />
60-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\92a51bc7c02ff55fe83543917b2f6d99\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
62-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
63                android:value="androidx.startup" />
63-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
64        </provider>
65
66        <activity
66-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4dd3fdaaff8835a6d69528f823645a30\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:23:9-26:79
67            android:name="androidx.activity.ComponentActivity"
67-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4dd3fdaaff8835a6d69528f823645a30\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:24:13-63
68            android:exported="true"
68-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4dd3fdaaff8835a6d69528f823645a30\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:25:13-36
69            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
69-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\4dd3fdaaff8835a6d69528f823645a30\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:26:13-76
70
71        <receiver
71-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
72            android:name="androidx.profileinstaller.ProfileInstallReceiver"
72-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
73            android:directBootAware="false"
73-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
74            android:enabled="true"
74-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
75            android:exported="true"
75-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
76            android:permission="android.permission.DUMP" >
76-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
78                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
78-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
78-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
81                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
81-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
81-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
84                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
84-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
84-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
85            </intent-filter>
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
87                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
87-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
87-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\2c22fbdc8fc9c90626b81f8a274df265\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
88            </intent-filter>
89        </receiver>
90    </application>
91
92</manifest>
