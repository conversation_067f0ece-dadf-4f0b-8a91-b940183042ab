{"logs": [{"outputFile": "com.example.tik555.app-mergeDebugResources-2:/values-in/values-in.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\ff3f81a0c1699dcfcdb03be15b69c061\\transformed\\material-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "95", "endOffsets": "146"}, "to": {"startLines": "141", "startColumns": "4", "startOffsets": "12123", "endColumns": "95", "endOffsets": "12214"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\402c9dcc315b2462c9ffa2b8bd9a3c9c\\transformed\\ui-release\\res\\values-in\\values-in.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,275,357,455,555,641,724,815,902,987,1069,1152,1224,1316,1393,1470,1543,1621,1687", "endColumns": "89,81,97,99,85,82,90,86,84,81,82,71,91,76,76,72,77,65,118", "endOffsets": "270,352,450,550,636,719,810,897,982,1064,1147,1219,1311,1388,1465,1538,1616,1682,1801"}, "to": {"startLines": "27,28,29,30,31,82,83,142,143,144,145,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1505,1595,1677,1775,1875,5664,5747,12219,12306,12391,12473,12556,12628,12720,12797,12874,13048,13126,13192", "endColumns": "89,81,97,99,85,82,90,86,84,81,82,71,91,76,76,72,77,65,118", "endOffsets": "1590,1672,1770,1870,1956,5742,5833,12301,12386,12468,12551,12623,12715,12792,12869,12942,13121,13187,13306"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\19d069744fa00860960c2518b4a58305\\transformed\\foundation-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,220", "endColumns": "78,85,89", "endOffsets": "129,215,305"}, "to": {"startLines": "19,155,156", "startColumns": "4,4,4", "startOffsets": "696,13311,13397", "endColumns": "78,85,89", "endOffsets": "770,13392,13482"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\95d12178e9e01a63c029617100d83575\\transformed\\core-1.16.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "20,21,22,23,24,25,26,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "775,870,972,1069,1166,1272,1390,12947", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "865,967,1064,1161,1267,1385,1500,13043"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0fd183eed73303e0d8adf1f47d0f3fe5\\transformed\\media3-exoplayer-1.4.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,185,250,313,389,453,553,647", "endColumns": "67,61,64,62,75,63,99,93,68", "endOffsets": "118,180,245,308,384,448,548,642,711"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3935,4003,4065,4130,4193,4269,4333,4433,4527", "endColumns": "67,61,64,62,75,63,99,93,68", "endOffsets": "3998,4060,4125,4188,4264,4328,4428,4522,4591"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\82da4bd88c75535472e099c38aac915b\\transformed\\material3-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,289,401,517,615,721,844,991,1114,1264,1351,1455,1548,1652,1770,1890,1999,2139,2277,2406,2584,2706,2826,2949,3072,3166,3267,3387,3520,3622,3729,3836,3978,4125,4234,4334,4410,4506,4601,4719,4808,4893,4992,5072,5155,5254,5353,5450,5550,5637,5740,5839,5943,6060,6140,6245", "endColumns": "118,114,111,115,97,105,122,146,122,149,86,103,92,103,117,119,108,139,137,128,177,121,119,122,122,93,100,119,132,101,106,106,141,146,108,99,75,95,94,117,88,84,98,79,82,98,98,96,99,86,102,98,103,116,79,104,94", "endOffsets": "169,284,396,512,610,716,839,986,1109,1259,1346,1450,1543,1647,1765,1885,1994,2134,2272,2401,2579,2701,2821,2944,3067,3161,3262,3382,3515,3617,3724,3831,3973,4120,4229,4329,4405,4501,4596,4714,4803,4888,4987,5067,5150,5249,5348,5445,5545,5632,5735,5834,5938,6055,6135,6240,6335"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5838,5957,6072,6184,6300,6398,6504,6627,6774,6897,7047,7134,7238,7331,7435,7553,7673,7782,7922,8060,8189,8367,8489,8609,8732,8855,8949,9050,9170,9303,9405,9512,9619,9761,9908,10017,10117,10193,10289,10384,10502,10591,10676,10775,10855,10938,11037,11136,11233,11333,11420,11523,11622,11726,11843,11923,12028", "endColumns": "118,114,111,115,97,105,122,146,122,149,86,103,92,103,117,119,108,139,137,128,177,121,119,122,122,93,100,119,132,101,106,106,141,146,108,99,75,95,94,117,88,84,98,79,82,98,98,96,99,86,102,98,103,116,79,104,94", "endOffsets": "5952,6067,6179,6295,6393,6499,6622,6769,6892,7042,7129,7233,7326,7430,7548,7668,7777,7917,8055,8184,8362,8484,8604,8727,8850,8944,9045,9165,9298,9400,9507,9614,9756,9903,10012,10112,10188,10284,10379,10497,10586,10671,10770,10850,10933,11032,11131,11228,11328,11415,11518,11617,11721,11838,11918,12023,12118"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cb4e11135627ad7155a925c218efe46e\\transformed\\media3-ui-1.4.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,646,729,814,890,978,1071,1148,1217,1313,1407,1471,1535,1601,1674,1789,1907,2023,2095,2175,2245,2319,2403,2489,2556,2620,2673,2731,2779,2840,2904,2966,3029,3095,3157,3220,3286,3338,3400,3476,3552,3614", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,82,84,75,87,92,76,68,95,93,63,63,65,72,114,117,115,71,79,69,73,83,85,66,63,52,57,47,60,63,61,62,65,61,62,65,51,61,75,75,61,73", "endOffsets": "280,467,641,724,809,885,973,1066,1143,1212,1308,1402,1466,1530,1596,1669,1784,1902,2018,2090,2170,2240,2314,2398,2484,2551,2615,2668,2726,2774,2835,2899,2961,3024,3090,3152,3215,3281,3333,3395,3471,3547,3609,3683"}, "to": {"startLines": "2,11,15,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,522,1961,2044,2129,2205,2293,2386,2463,2532,2628,2722,2786,2850,2916,2989,3104,3222,3338,3410,3490,3560,3634,3718,3804,3871,4596,4649,4707,4755,4816,4880,4942,5005,5071,5133,5196,5262,5314,5376,5452,5528,5590", "endLines": "10,14,18,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,82,84,75,87,92,76,68,95,93,63,63,65,72,114,117,115,71,79,69,73,83,85,66,63,52,57,47,60,63,61,62,65,61,62,65,51,61,75,75,61,73", "endOffsets": "330,517,691,2039,2124,2200,2288,2381,2458,2527,2623,2717,2781,2845,2911,2984,3099,3217,3333,3405,3485,3555,3629,3713,3799,3866,3930,4644,4702,4750,4811,4875,4937,5000,5066,5128,5191,5257,5309,5371,5447,5523,5585,5659"}}]}]}