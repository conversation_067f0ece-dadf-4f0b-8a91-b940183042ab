package com.example.tik555.repository

import com.example.tik555.data.VideoItem
import com.example.tik555.network.NetworkModule
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 视频数据仓库
 */
class VideoRepository {
    
    private val apiService = NetworkModule.videoApiService
    
    /**
     * 获取视频列表
     */
    suspend fun getVideoList(): Result<List<VideoItem>> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getVideoList()
            if (response.isSuccessful) {
                val videoResponse = response.body()
                if (videoResponse?.code == 200) {
                    Result.success(videoResponse.data.list)
                } else {
                    Result.failure(Exception("API返回错误: ${videoResponse?.code}"))
                }
            } else {
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
