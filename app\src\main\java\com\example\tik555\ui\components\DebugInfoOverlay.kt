package com.example.tik555.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.tik555.data.VideoItem

/**
 * 调试信息覆盖层
 * 显示在右下角的调试按钮和信息面板
 */
@Composable
fun DebugInfoOverlay(
    currentVideoIndex: Int,
    currentVideoItem: VideoItem?,
    playerInfo: Map<String, Any>,
    modifier: Modifier = Modifier
) {
    var showDebugInfo by remember { mutableStateOf(false) }
    
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.BottomEnd
    ) {
        // 调试信息面板
        if (showDebugInfo) {
            DebugInfoPanel(
                currentVideoIndex = currentVideoIndex,
                currentVideoItem = currentVideoItem,
                playerInfo = playerInfo,
                modifier = Modifier
                    .padding(16.dp)
                    .background(
                        Color.Black.copy(alpha = 0.8f),
                        shape = MaterialTheme.shapes.medium
                    )
                    .padding(12.dp)
            )
        }
        
        // 调试按钮
        Box(
            modifier = Modifier
                .padding(16.dp)
                .size(56.dp)
                .clip(CircleShape)
                .background(Color.Red.copy(alpha = 0.7f))
                .clickable { showDebugInfo = !showDebugInfo },
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "DEBUG",
                color = Color.White,
                fontSize = 10.sp,
                fontFamily = FontFamily.Monospace
            )
        }
    }
}

@Composable
private fun DebugInfoPanel(
    currentVideoIndex: Int,
    currentVideoItem: VideoItem?,
    playerInfo: Map<String, Any>,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Text(
            text = "=== 调试信息 ===",
            color = Color.White,
            fontSize = 14.sp,
            fontFamily = FontFamily.Monospace
        )
        
        // 当前视频信息
        Text(
            text = "当前视频索引: $currentVideoIndex",
            color = Color.White,
            fontSize = 12.sp,
            fontFamily = FontFamily.Monospace
        )
        
        currentVideoItem?.let { video ->
            Text(
                text = "视频ID: ${video.videoId}",
                color = Color.White,
                fontSize = 12.sp,
                fontFamily = FontFamily.Monospace
            )
            
            Text(
                text = "视频尺寸: ${video.width}x${video.height}",
                color = Color.White,
                fontSize = 12.sp,
                fontFamily = FontFamily.Monospace
            )
            
            Text(
                text = "宽高比: ${"%.2f".format(video.aspectRatio)}",
                color = Color.White,
                fontSize = 12.sp,
                fontFamily = FontFamily.Monospace
            )
        }
        
        // 播放器信息
        Text(
            text = "总播放器数: ${playerInfo["totalPlayers"]}",
            color = Color.White,
            fontSize = 12.sp,
            fontFamily = FontFamily.Monospace
        )
        
        Text(
            text = "活跃播放器: ${playerInfo["activePlayers"]}",
            color = Color.White,
            fontSize = 12.sp,
            fontFamily = FontFamily.Monospace
        )
        
        Text(
            text = "缓存大小: ${formatCacheSize(playerInfo["cacheSize"] as? Long ?: 0L)}",
            color = Color.White,
            fontSize = 12.sp,
            fontFamily = FontFamily.Monospace
        )
        
        // 播放器状态
        (playerInfo["playerStates"] as? List<*>)?.forEach { state ->
            if (state is Map<*, *>) {
                Text(
                    text = "播放器[${state["videoIndex"]}]: ${if (state["isPlaying"] == true) "播放中" else "暂停"}",
                    color = if (state["isPlaying"] == true) Color.Green else Color.Yellow,
                    fontSize = 11.sp,
                    fontFamily = FontFamily.Monospace
                )
            }
        }
    }
}

private fun formatCacheSize(bytes: Long): String {
    val mb = bytes / (1024 * 1024)
    return "${mb}MB"
}
