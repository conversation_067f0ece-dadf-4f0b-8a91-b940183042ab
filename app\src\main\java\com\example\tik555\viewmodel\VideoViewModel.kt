package com.example.tik555.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.tik555.data.VideoItem
import com.example.tik555.player.PlayerManager
import com.example.tik555.repository.VideoRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 视频页面ViewModel
 * 管理视频列表、播放器状态和数据加载
 */
class VideoViewModel : ViewModel() {
    
    private val repository = VideoRepository()
    private var playerManager: PlayerManager? = null
    
    // 视频列表
    private val _videoList = MutableStateFlow<List<VideoItem>>(emptyList())
    val videoList: StateFlow<List<VideoItem>> = _videoList.asStateFlow()
    
    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    // 错误信息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    // 当前页面索引
    private val _currentPage = MutableStateFlow(0)
    val currentPage: StateFlow<Int> = _currentPage.asStateFlow()
    
    // 播放器信息
    private val _playerInfo = MutableStateFlow<Map<String, Any>>(emptyMap())
    val playerInfo: StateFlow<Map<String, Any>> = _playerInfo.asStateFlow()
    
    /**
     * 设置播放器管理器
     */
    fun setPlayerManager(manager: PlayerManager) {
        playerManager = manager
        updatePlayerInfo()
    }
    
    /**
     * 加载初始视频列表
     */
    fun loadInitialVideos() {
        viewModelScope.launch {
            _isLoading.value = true
            _errorMessage.value = null
            
            repository.getVideoList().fold(
                onSuccess = { videos ->
                    _videoList.value = videos
                    _isLoading.value = false
                },
                onFailure = { error ->
                    _errorMessage.value = error.message
                    _isLoading.value = false
                }
            )
        }
    }
    
    /**
     * 加载更多视频
     */
    fun loadMoreVideos() {
        if (_isLoading.value) return
        
        viewModelScope.launch {
            _isLoading.value = true
            
            repository.getVideoList().fold(
                onSuccess = { newVideos ->
                    val currentList = _videoList.value.toMutableList()
                    currentList.addAll(newVideos)
                    _videoList.value = currentList
                    _isLoading.value = false
                },
                onFailure = { error ->
                    _errorMessage.value = error.message
                    _isLoading.value = false
                }
            )
        }
    }
    
    /**
     * 更新当前页面
     */
    fun updateCurrentPage(page: Int) {
        _currentPage.value = page
        updatePlayerInfo()
    }
    
    /**
     * 预加载视频
     */
    fun preloadVideos(currentIndex: Int) {
        playerManager?.preloadVideos(currentIndex, _videoList.value)
        updatePlayerInfo()
    }
    
    /**
     * 更新播放器信息
     */
    private fun updatePlayerInfo() {
        playerManager?.let { manager ->
            _playerInfo.value = manager.getPlayerInfo()
        }
    }
    
    /**
     * 清理错误信息
     */
    fun clearError() {
        _errorMessage.value = null
    }
    
    override fun onCleared() {
        super.onCleared()
        playerManager?.release()
    }
}
