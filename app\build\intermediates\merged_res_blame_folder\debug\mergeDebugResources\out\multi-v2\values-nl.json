{"logs": [{"outputFile": "com.example.tik555.app-mergeDebugResources-2:/values-nl/values-nl.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\19d069744fa00860960c2518b4a58305\\transformed\\foundation-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,228", "endColumns": "86,85,85", "endOffsets": "137,223,309"}, "to": {"startLines": "19,155,156", "startColumns": "4,4,4", "startOffsets": "727,13351,13437", "endColumns": "86,85,85", "endOffsets": "809,13432,13518"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\95d12178e9e01a63c029617100d83575\\transformed\\core-1.16.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "20,21,22,23,24,25,26,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "814,916,1018,1118,1218,1325,1429,12976", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "911,1013,1113,1213,1320,1424,1543,13072"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\82da4bd88c75535472e099c38aac915b\\transformed\\material3-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,294,415,533,633,731,846,998,1119,1261,1346,1445,1541,1644,1762,1883,1987,2118,2246,2382,2560,2691,2811,2932,3067,3164,3264,3384,3513,3613,3720,3823,3960,4100,4206,4310,4394,4494,4591,4702,4789,4876,4981,5061,5144,5243,5347,5442,5541,5629,5739,5840,5945,6065,6145,6246", "endColumns": "121,116,120,117,99,97,114,151,120,141,84,98,95,102,117,120,103,130,127,135,177,130,119,120,134,96,99,119,128,99,106,102,136,139,105,103,83,99,96,110,86,86,104,79,82,98,103,94,98,87,109,100,104,119,79,100,94", "endOffsets": "172,289,410,528,628,726,841,993,1114,1256,1341,1440,1536,1639,1757,1878,1982,2113,2241,2377,2555,2686,2806,2927,3062,3159,3259,3379,3508,3608,3715,3818,3955,4095,4201,4305,4389,4489,4586,4697,4784,4871,4976,5056,5139,5238,5342,5437,5536,5624,5734,5835,5940,6060,6140,6241,6336"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5879,6001,6118,6239,6357,6457,6555,6670,6822,6943,7085,7170,7269,7365,7468,7586,7707,7811,7942,8070,8206,8384,8515,8635,8756,8891,8988,9088,9208,9337,9437,9544,9647,9784,9924,10030,10134,10218,10318,10415,10526,10613,10700,10805,10885,10968,11067,11171,11266,11365,11453,11563,11664,11769,11889,11969,12070", "endColumns": "121,116,120,117,99,97,114,151,120,141,84,98,95,102,117,120,103,130,127,135,177,130,119,120,134,96,99,119,128,99,106,102,136,139,105,103,83,99,96,110,86,86,104,79,82,98,103,94,98,87,109,100,104,119,79,100,94", "endOffsets": "5996,6113,6234,6352,6452,6550,6665,6817,6938,7080,7165,7264,7360,7463,7581,7702,7806,7937,8065,8201,8379,8510,8630,8751,8886,8983,9083,9203,9332,9432,9539,9642,9779,9919,10025,10129,10213,10313,10410,10521,10608,10695,10800,10880,10963,11062,11166,11261,11360,11448,11558,11659,11764,11884,11964,12065,12160"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0fd183eed73303e0d8adf1f47d0f3fe5\\transformed\\media3-exoplayer-1.4.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,190,254,321,398,467,556,639", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "121,185,249,316,393,462,551,634,706"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4001,4072,4136,4200,4267,4344,4413,4502,4585", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "4067,4131,4195,4262,4339,4408,4497,4580,4652"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cb4e11135627ad7155a925c218efe46e\\transformed\\media3-ui-1.4.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,486,677,766,854,934,1027,1120,1193,1260,1362,1460,1528,1595,1660,1729,1848,1967,2082,2155,2234,2309,2378,2461,2543,2609,2674,2727,2785,2833,2894,2959,3021,3086,3154,3212,3270,3336,3388,3450,3526,3602,3657", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,118,118,114,72,78,74,68,82,81,65,64,52,57,47,60,64,61,64,67,57,57,65,51,61,75,75,54,66", "endOffsets": "281,481,672,761,849,929,1022,1115,1188,1255,1357,1455,1523,1590,1655,1724,1843,1962,2077,2150,2229,2304,2373,2456,2538,2604,2669,2722,2780,2828,2889,2954,3016,3081,3149,3207,3265,3331,3383,3445,3521,3597,3652,3719"}, "to": {"startLines": "2,11,15,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,536,2004,2093,2181,2261,2354,2447,2520,2587,2689,2787,2855,2922,2987,3056,3175,3294,3409,3482,3561,3636,3705,3788,3870,3936,4657,4710,4768,4816,4877,4942,5004,5069,5137,5195,5253,5319,5371,5433,5509,5585,5640", "endLines": "10,14,18,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,118,118,114,72,78,74,68,82,81,65,64,52,57,47,60,64,61,64,67,57,57,65,51,61,75,75,54,66", "endOffsets": "331,531,722,2088,2176,2256,2349,2442,2515,2582,2684,2782,2850,2917,2982,3051,3170,3289,3404,3477,3556,3631,3700,3783,3865,3931,3996,4705,4763,4811,4872,4937,4999,5064,5132,5190,5248,5314,5366,5428,5504,5580,5635,5702"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\402c9dcc315b2462c9ffa2b8bd9a3c9c\\transformed\\ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,285,368,465,564,649,725,821,908,997,1078,1161,1238,1324,1399,1471,1542,1626,1696", "endColumns": "91,82,96,98,84,75,95,86,88,80,82,76,85,74,71,70,83,69,119", "endOffsets": "280,363,460,559,644,720,816,903,992,1073,1156,1233,1319,1394,1466,1537,1621,1691,1811"}, "to": {"startLines": "27,28,29,30,31,82,83,142,143,144,145,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1548,1640,1723,1820,1919,5707,5783,12255,12342,12431,12512,12595,12672,12758,12833,12905,13077,13161,13231", "endColumns": "91,82,96,98,84,75,95,86,88,80,82,76,85,74,71,70,83,69,119", "endOffsets": "1635,1718,1815,1914,1999,5778,5874,12337,12426,12507,12590,12667,12753,12828,12900,12971,13156,13226,13346"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\ff3f81a0c1699dcfcdb03be15b69c061\\transformed\\material-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "141", "startColumns": "4", "startOffsets": "12165", "endColumns": "89", "endOffsets": "12250"}}]}]}