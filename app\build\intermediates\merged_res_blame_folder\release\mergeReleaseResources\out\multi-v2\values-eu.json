{"logs": [{"outputFile": "com.example.tik555.app-mergeReleaseResources-2:/values-eu/values-eu.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "192,283,364,465,569,661,737,824,913,997,1085,1175,1249,1334,1411,1493,1571,1648,1716", "endColumns": "90,80,100,103,91,75,86,88,83,87,89,73,84,76,81,77,76,67,119", "endOffsets": "278,359,460,564,656,732,819,908,992,1080,1170,1244,1329,1406,1488,1566,1643,1711,1831"}, "to": {"startLines": "27,28,29,30,31,82,83,141,142,143,144,145,146,147,148,149,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1524,1615,1696,1797,1901,5877,5953,12487,12576,12660,12748,12838,12912,12997,13074,13156,13335,13412,13480", "endColumns": "90,80,100,103,91,75,86,88,83,87,89,73,84,76,81,77,76,67,119", "endOffsets": "1610,1691,1792,1896,1988,5948,6035,12571,12655,12743,12833,12907,12992,13069,13151,13229,13407,13475,13595"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "20,21,22,23,24,25,26,150", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "793,891,994,1094,1197,1302,1405,13234", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "886,989,1089,1192,1297,1400,1519,13330"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\e874b47f582ea1f96bd82c21770ae83f\\transformed\\media3-ui-1.4.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,657,741,827,902,1001,1092,1187,1255,1351,1447,1514,1586,1651,1722,1849,1971,2094,2163,2252,2324,2419,2519,2621,2687,2754,2807,2865,2914,2975,3037,3109,3173,3240,3305,3369,3436,3490,3557,3638,3719,3775", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,83,85,74,98,90,94,67,95,95,66,71,64,70,126,121,122,68,88,71,94,99,101,65,66,52,57,48,60,61,71,63,66,64,63,66,53,66,80,80,55,67", "endOffsets": "281,470,652,736,822,897,996,1087,1182,1250,1346,1442,1509,1581,1646,1717,1844,1966,2089,2158,2247,2319,2414,2514,2616,2682,2749,2802,2860,2909,2970,3032,3104,3168,3235,3300,3364,3431,3485,3552,3633,3714,3770,3838"}, "to": {"startLines": "2,11,15,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,525,1993,2077,2163,2238,2337,2428,2523,2591,2687,2783,2850,2922,2987,3058,3185,3307,3430,3499,3588,3660,3755,3855,3957,4023,4788,4841,4899,4948,5009,5071,5143,5207,5274,5339,5403,5470,5524,5591,5672,5753,5809", "endLines": "10,14,18,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,83,85,74,98,90,94,67,95,95,66,71,64,70,126,121,122,68,88,71,94,99,101,65,66,52,57,48,60,61,71,63,66,64,63,66,53,66,80,80,55,67", "endOffsets": "331,520,702,2072,2158,2233,2332,2423,2518,2586,2682,2778,2845,2917,2982,3053,3180,3302,3425,3494,3583,3655,3750,3850,3952,4018,4085,4836,4894,4943,5004,5066,5138,5202,5269,5334,5398,5465,5519,5586,5667,5748,5804,5872"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,227", "endColumns": "85,85,88", "endOffsets": "136,222,311"}, "to": {"startLines": "19,154,155", "startColumns": "4,4,4", "startOffsets": "707,13600,13686", "endColumns": "85,85,88", "endOffsets": "788,13681,13770"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,349,476,621,750,848,963,1103,1222,1367,1451,1556,1652,1752,1871,1992,2102,2245,2389,2524,2715,2840,2962,3086,3208,3305,3402,3530,3665,3763,3866,3972,4119,4270,4378,4478,4554,4650,4745,4864,4951,5039,5149,5229,5314,5409,5512,5603,5702,5791,5899,5999,6105,6223,6303,6407", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,118,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "197,344,471,616,745,843,958,1098,1217,1362,1446,1551,1647,1747,1866,1987,2097,2240,2384,2519,2710,2835,2957,3081,3203,3300,3397,3525,3660,3758,3861,3967,4114,4265,4373,4473,4549,4645,4740,4859,4946,5034,5144,5224,5309,5404,5507,5598,5697,5786,5894,5994,6100,6218,6298,6402,6497"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6040,6187,6334,6461,6606,6735,6833,6948,7088,7207,7352,7436,7541,7637,7737,7856,7977,8087,8230,8374,8509,8700,8825,8947,9071,9193,9290,9387,9515,9650,9748,9851,9957,10104,10255,10363,10463,10539,10635,10730,10849,10936,11024,11134,11214,11299,11394,11497,11588,11687,11776,11884,11984,12090,12208,12288,12392", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,118,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "6182,6329,6456,6601,6730,6828,6943,7083,7202,7347,7431,7536,7632,7732,7851,7972,8082,8225,8369,8504,8695,8820,8942,9066,9188,9285,9382,9510,9645,9743,9846,9952,10099,10250,10358,10458,10534,10630,10725,10844,10931,11019,11129,11209,11294,11389,11492,11583,11682,11771,11879,11979,12085,12203,12283,12387,12482"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\457147f8f67deaf2a3ec4b5417c706c3\\transformed\\media3-exoplayer-1.4.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,187,253,325,402,476,587,685", "endColumns": "68,62,65,71,76,73,110,97,67", "endOffsets": "119,182,248,320,397,471,582,680,748"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4090,4159,4222,4288,4360,4437,4511,4622,4720", "endColumns": "68,62,65,71,76,73,110,97,67", "endOffsets": "4154,4217,4283,4355,4432,4506,4617,4715,4783"}}]}]}