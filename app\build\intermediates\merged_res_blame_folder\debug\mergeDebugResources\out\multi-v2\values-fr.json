{"logs": [{"outputFile": "com.example.tik555.app-mergeDebugResources-2:/values-fr/values-fr.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0fd183eed73303e0d8adf1f47d0f3fe5\\transformed\\media3-exoplayer-1.4.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,263,335,418,495,592,685", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "124,189,258,330,413,490,587,680,763"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4102,4176,4241,4310,4382,4465,4542,4639,4732", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "4171,4236,4305,4377,4460,4537,4634,4727,4810"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\19d069744fa00860960c2518b4a58305\\transformed\\foundation-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,140,229", "endColumns": "84,88,94", "endOffsets": "135,224,319"}, "to": {"startLines": "19,155,156", "startColumns": "4,4,4", "startOffsets": "742,13679,13768", "endColumns": "84,88,94", "endOffsets": "822,13763,13858"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\95d12178e9e01a63c029617100d83575\\transformed\\core-1.16.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "20,21,22,23,24,25,26,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "827,925,1027,1126,1228,1332,1436,13309", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "920,1022,1121,1223,1327,1431,1549,13405"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\82da4bd88c75535472e099c38aac915b\\transformed\\material3-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,293,415,536,635,729,841,985,1104,1251,1335,1435,1536,1637,1758,1885,1990,2140,2286,2416,2608,2734,2852,2975,3108,3210,3315,3439,3564,3666,3773,3878,4023,4175,4284,4393,4480,4573,4668,4788,4879,4965,5072,5152,5237,5339,5451,5549,5649,5737,5853,5954,6057,6189,6269,6379", "endColumns": "119,117,121,120,98,93,111,143,118,146,83,99,100,100,120,126,104,149,145,129,191,125,117,122,132,101,104,123,124,101,106,104,144,151,108,108,86,92,94,119,90,85,106,79,84,101,111,97,99,87,115,100,102,131,79,109,97", "endOffsets": "170,288,410,531,630,724,836,980,1099,1246,1330,1430,1531,1632,1753,1880,1985,2135,2281,2411,2603,2729,2847,2970,3103,3205,3310,3434,3559,3661,3768,3873,4018,4170,4279,4388,4475,4568,4663,4783,4874,4960,5067,5147,5232,5334,5446,5544,5644,5732,5848,5949,6052,6184,6264,6374,6472"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6065,6185,6303,6425,6546,6645,6739,6851,6995,7114,7261,7345,7445,7546,7647,7768,7895,8000,8150,8296,8426,8618,8744,8862,8985,9118,9220,9325,9449,9574,9676,9783,9888,10033,10185,10294,10403,10490,10583,10678,10798,10889,10975,11082,11162,11247,11349,11461,11559,11659,11747,11863,11964,12067,12199,12279,12389", "endColumns": "119,117,121,120,98,93,111,143,118,146,83,99,100,100,120,126,104,149,145,129,191,125,117,122,132,101,104,123,124,101,106,104,144,151,108,108,86,92,94,119,90,85,106,79,84,101,111,97,99,87,115,100,102,131,79,109,97", "endOffsets": "6180,6298,6420,6541,6640,6734,6846,6990,7109,7256,7340,7440,7541,7642,7763,7890,7995,8145,8291,8421,8613,8739,8857,8980,9113,9215,9320,9444,9569,9671,9778,9883,10028,10180,10289,10398,10485,10578,10673,10793,10884,10970,11077,11157,11242,11344,11456,11554,11654,11742,11858,11959,12062,12194,12274,12384,12482"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\ff3f81a0c1699dcfcdb03be15b69c061\\transformed\\material-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "141", "startColumns": "4", "startOffsets": "12487", "endColumns": "88", "endOffsets": "12571"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cb4e11135627ad7155a925c218efe46e\\transformed\\media3-ui-1.4.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,483,692,781,872,951,1049,1146,1225,1291,1397,1504,1569,1635,1699,1771,1891,2014,2136,2211,2299,2372,2452,2543,2636,2702,2766,2819,2879,2927,2988,3059,3130,3197,3275,3340,3399,3465,3517,3577,3651,3725,3779", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,119,122,121,74,87,72,79,90,92,65,63,52,59,47,60,70,70,66,77,64,58,65,51,59,73,73,53,65", "endOffsets": "286,478,687,776,867,946,1044,1141,1220,1286,1392,1499,1564,1630,1694,1766,1886,2009,2131,2206,2294,2367,2447,2538,2631,2697,2761,2814,2874,2922,2983,3054,3125,3192,3270,3335,3394,3460,3512,3572,3646,3720,3774,3840"}, "to": {"startLines": "2,11,15,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,341,533,2028,2117,2208,2287,2385,2482,2561,2627,2733,2840,2905,2971,3035,3107,3227,3350,3472,3547,3635,3708,3788,3879,3972,4038,4815,4868,4928,4976,5037,5108,5179,5246,5324,5389,5448,5514,5566,5626,5700,5774,5828", "endLines": "10,14,18,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,119,122,121,74,87,72,79,90,92,65,63,52,59,47,60,70,70,66,77,64,58,65,51,59,73,73,53,65", "endOffsets": "336,528,737,2112,2203,2282,2380,2477,2556,2622,2728,2835,2900,2966,3030,3102,3222,3345,3467,3542,3630,3703,3783,3874,3967,4033,4097,4863,4923,4971,5032,5103,5174,5241,5319,5384,5443,5509,5561,5621,5695,5769,5823,5889"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\402c9dcc315b2462c9ffa2b8bd9a3c9c\\transformed\\ui-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "191,290,378,478,578,665,744,836,928,1015,1096,1181,1257,1342,1417,1495,1569,1647,1716", "endColumns": "98,87,99,99,86,78,91,91,86,80,84,75,84,74,77,73,77,68,121", "endOffsets": "285,373,473,573,660,739,831,923,1010,1091,1176,1252,1337,1412,1490,1564,1642,1711,1833"}, "to": {"startLines": "27,28,29,30,31,82,83,142,143,144,145,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1554,1653,1741,1841,1941,5894,5973,12576,12668,12755,12836,12921,12997,13082,13157,13235,13410,13488,13557", "endColumns": "98,87,99,99,86,78,91,91,86,80,84,75,84,74,77,73,77,68,121", "endOffsets": "1648,1736,1836,1936,2023,5968,6060,12663,12750,12831,12916,12992,13077,13152,13230,13304,13483,13552,13674"}}]}]}