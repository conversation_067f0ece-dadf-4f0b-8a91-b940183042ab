{"logs": [{"outputFile": "com.example.tik555.app-mergeDebugResources-2:/values-vi/values-vi.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\dc0228f35d5d9920c8fe531ae3e0365a\\transformed\\media3-exoplayer-1.7.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,191,259,328,419,489,579,667", "endColumns": "72,62,67,68,90,69,89,87,79", "endOffsets": "123,186,254,323,414,484,574,662,742"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4006,4079,4142,4210,4279,4370,4440,4530,4618", "endColumns": "72,62,67,68,90,69,89,87,79", "endOffsets": "4074,4137,4205,4274,4365,4435,4525,4613,4693"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "20,21,22,23,24,25,26,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "776,873,975,1074,1174,1277,1390,12914", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "868,970,1069,1169,1272,1385,1501,13010"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,281,367,473,573,665,750,843,937,1018,1108,1199,1271,1358,1434,1511,1587,1664,1730", "endColumns": "95,85,105,99,91,84,92,93,80,89,90,71,86,75,76,75,76,65,113", "endOffsets": "276,362,468,568,660,745,838,932,1013,1103,1194,1266,1353,1429,1506,1582,1659,1725,1839"}, "to": {"startLines": "27,28,29,30,31,82,83,142,143,144,145,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1506,1602,1688,1794,1894,5771,5856,12170,12264,12345,12435,12526,12598,12685,12761,12838,13015,13092,13158", "endColumns": "95,85,105,99,91,84,92,93,80,89,90,71,86,75,76,75,76,65,113", "endOffsets": "1597,1683,1789,1889,1981,5851,5944,12259,12340,12430,12521,12593,12680,12756,12833,12909,13087,13153,13267"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\17d8797a605bf267136b984c4f4c6900\\transformed\\media3-ui-1.7.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,290,473,647,723,798,870,973,1074,1153,1221,1320,1421,1489,1552,1615,1683,1813,1933,2060,2128,2206,2276,2361,2446,2530,2593,2667,2720,2781,2831,2892,2954,3020,3084,3149,3210,3269,3338,3396,3456,3530,3604,3667", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,75,74,71,102,100,78,67,98,100,67,62,62,67,129,119,126,67,77,69,84,84,83,62,73,52,60,49,60,61,65,63,64,60,58,68,57,59,73,73,62,72", "endOffsets": "285,468,642,718,793,865,968,1069,1148,1216,1315,1416,1484,1547,1610,1678,1808,1928,2055,2123,2201,2271,2356,2441,2525,2588,2662,2715,2776,2826,2887,2949,3015,3079,3144,3205,3264,3333,3391,3451,3525,3599,3662,3735"}, "to": {"startLines": "2,11,15,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,340,523,1986,2062,2137,2209,2312,2413,2492,2560,2659,2760,2828,2891,2954,3022,3152,3272,3399,3467,3545,3615,3700,3785,3869,3932,4698,4751,4812,4862,4923,4985,5051,5115,5180,5241,5300,5369,5427,5487,5561,5635,5698", "endLines": "10,14,18,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,75,74,71,102,100,78,67,98,100,67,62,62,67,129,119,126,67,77,69,84,84,83,62,73,52,60,49,60,61,65,63,64,60,58,68,57,59,73,73,62,72", "endOffsets": "335,518,692,2057,2132,2204,2307,2408,2487,2555,2654,2755,2823,2886,2949,3017,3147,3267,3394,3462,3540,3610,3695,3780,3864,3927,4001,4746,4807,4857,4918,4980,5046,5110,5175,5236,5295,5364,5422,5482,5556,5630,5693,5766"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\8a2e8e7142c38a8f2ee7133e4de5b14c\\transformed\\material-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "141", "startColumns": "4", "startOffsets": "12079", "endColumns": "90", "endOffsets": "12165"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,289,396,513,614,709,821,958,1078,1219,1303,1406,1495,1591,1710,1833,1941,2068,2191,2318,2477,2604,2727,2847,2966,3056,3156,3274,3407,3502,3608,3715,3838,3968,4076,4172,4251,4348,4444,4555,4644,4728,4835,4915,4998,5097,5195,5290,5389,5475,5576,5674,5776,5892,5972,6081", "endColumns": "118,114,106,116,100,94,111,136,119,140,83,102,88,95,118,122,107,126,122,126,158,126,122,119,118,89,99,117,132,94,105,106,122,129,107,95,78,96,95,110,88,83,106,79,82,98,97,94,98,85,100,97,101,115,79,108,103", "endOffsets": "169,284,391,508,609,704,816,953,1073,1214,1298,1401,1490,1586,1705,1828,1936,2063,2186,2313,2472,2599,2722,2842,2961,3051,3151,3269,3402,3497,3603,3710,3833,3963,4071,4167,4246,4343,4439,4550,4639,4723,4830,4910,4993,5092,5190,5285,5384,5470,5571,5669,5771,5887,5967,6076,6180"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5949,6068,6183,6290,6407,6508,6603,6715,6852,6972,7113,7197,7300,7389,7485,7604,7727,7835,7962,8085,8212,8371,8498,8621,8741,8860,8950,9050,9168,9301,9396,9502,9609,9732,9862,9970,10066,10145,10242,10338,10449,10538,10622,10729,10809,10892,10991,11089,11184,11283,11369,11470,11568,11670,11786,11866,11975", "endColumns": "118,114,106,116,100,94,111,136,119,140,83,102,88,95,118,122,107,126,122,126,158,126,122,119,118,89,99,117,132,94,105,106,122,129,107,95,78,96,95,110,88,83,106,79,82,98,97,94,98,85,100,97,101,115,79,108,103", "endOffsets": "6063,6178,6285,6402,6503,6598,6710,6847,6967,7108,7192,7295,7384,7480,7599,7722,7830,7957,8080,8207,8366,8493,8616,8736,8855,8945,9045,9163,9296,9391,9497,9604,9727,9857,9965,10061,10140,10237,10333,10444,10533,10617,10724,10804,10887,10986,11084,11179,11278,11364,11465,11563,11665,11781,11861,11970,12074"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,222", "endColumns": "78,87,86", "endOffsets": "129,217,304"}, "to": {"startLines": "19,155,156", "startColumns": "4,4,4", "startOffsets": "697,13272,13360", "endColumns": "78,87,86", "endOffsets": "771,13355,13442"}}]}]}