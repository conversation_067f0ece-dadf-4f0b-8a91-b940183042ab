{"logs": [{"outputFile": "com.example.tik555.app-mergeReleaseResources-2:/values-pl/values-pl.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\e874b47f582ea1f96bd82c21770ae83f\\transformed\\media3-ui-1.4.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,622,944,1022,1100,1183,1272,1361,1444,1511,1605,1699,1768,1834,1899,1971,2098,2221,2344,2420,2501,2574,2657,2754,2851,2919,2983,3036,3094,3142,3203,3276,3342,3406,3483,3550,3608,3675,3727,3794,3885,3976,4031", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,77,77,82,88,88,82,66,93,93,68,65,64,71,126,122,122,75,80,72,82,96,96,67,63,52,57,47,60,72,65,63,76,66,57,66,51,66,90,90,54,66", "endOffsets": "282,617,939,1017,1095,1178,1267,1356,1439,1506,1600,1694,1763,1829,1894,1966,2093,2216,2339,2415,2496,2569,2652,2749,2846,2914,2978,3031,3089,3137,3198,3271,3337,3401,3478,3545,3603,3670,3722,3789,3880,3971,4026,4093"}, "to": {"startLines": "2,11,17,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,672,2285,2363,2441,2524,2613,2702,2785,2852,2946,3040,3109,3175,3240,3312,3439,3562,3685,3761,3842,3915,3998,4095,4192,4260,4986,5039,5097,5145,5206,5279,5345,5409,5486,5553,5611,5678,5730,5797,5888,5979,6034", "endLines": "10,16,22,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "17,12,12,77,77,82,88,88,82,66,93,93,68,65,64,71,126,122,122,75,80,72,82,96,96,67,63,52,57,47,60,72,65,63,76,66,57,66,51,66,90,90,54,66", "endOffsets": "332,667,989,2358,2436,2519,2608,2697,2780,2847,2941,3035,3104,3170,3235,3307,3434,3557,3680,3756,3837,3910,3993,4090,4187,4255,4319,5034,5092,5140,5201,5274,5340,5404,5481,5548,5606,5673,5725,5792,5883,5974,6029,6096"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "24,25,26,27,28,29,30,154", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1077,1174,1276,1374,1473,1587,1692,13251", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "1169,1271,1369,1468,1582,1687,1809,13347"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\457147f8f67deaf2a3ec4b5417c706c3\\transformed\\media3-exoplayer-1.4.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,189,251,320,398,468,561,652", "endColumns": "72,60,61,68,77,69,92,90,64", "endOffsets": "123,184,246,315,393,463,556,647,712"}, "to": {"startLines": "60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4324,4397,4458,4520,4589,4667,4737,4830,4921", "endColumns": "72,60,61,68,77,69,92,90,64", "endOffsets": "4392,4453,4515,4584,4662,4732,4825,4916,4981"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,409,524,624,723,839,977,1099,1241,1325,1424,1516,1612,1729,1853,1957,2097,2233,2377,2538,2670,2791,2916,3037,3130,3230,3350,3474,3573,3677,3783,3924,4071,4182,4281,4355,4450,4546,4650,4737,4824,4936,5016,5103,5198,5303,5394,5503,5591,5697,5798,5908,6026,6106,6209", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,103,86,86,111,79,86,94,104,90,108,87,105,100,109,117,79,102,96", "endOffsets": "165,282,404,519,619,718,834,972,1094,1236,1320,1419,1511,1607,1724,1848,1952,2092,2228,2372,2533,2665,2786,2911,3032,3125,3225,3345,3469,3568,3672,3778,3919,4066,4177,4276,4350,4445,4541,4645,4732,4819,4931,5011,5098,5193,5298,5389,5498,5586,5692,5793,5903,6021,6101,6204,6301"}, "to": {"startLines": "88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6271,6386,6503,6625,6740,6840,6939,7055,7193,7315,7457,7541,7640,7732,7828,7945,8069,8173,8313,8449,8593,8754,8886,9007,9132,9253,9346,9446,9566,9690,9789,9893,9999,10140,10287,10398,10497,10571,10666,10762,10866,10953,11040,11152,11232,11319,11414,11519,11610,11719,11807,11913,12014,12124,12242,12322,12425", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,103,86,86,111,79,86,94,104,90,108,87,105,100,109,117,79,102,96", "endOffsets": "6381,6498,6620,6735,6835,6934,7050,7188,7310,7452,7536,7635,7727,7823,7940,8064,8168,8308,8444,8588,8749,8881,9002,9127,9248,9341,9441,9561,9685,9784,9888,9994,10135,10282,10393,10492,10566,10661,10757,10861,10948,11035,11147,11227,11314,11409,11514,11605,11714,11802,11908,12009,12119,12237,12317,12420,12517"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "189,284,369,478,583,660,737,830,920,1003,1086,1173,1245,1329,1405,1483,1559,1641,1709", "endColumns": "94,84,108,104,76,76,92,89,82,82,86,71,83,75,77,75,81,67,119", "endOffsets": "279,364,473,578,655,732,825,915,998,1081,1168,1240,1324,1400,1478,1554,1636,1704,1824"}, "to": {"startLines": "31,32,33,34,35,86,87,145,146,147,148,149,150,151,152,153,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1814,1909,1994,2103,2208,6101,6178,12522,12612,12695,12778,12865,12937,13021,13097,13175,13352,13434,13502", "endColumns": "94,84,108,104,76,76,92,89,82,82,86,71,83,75,77,75,81,67,119", "endOffsets": "1904,1989,2098,2203,2280,6173,6266,12607,12690,12773,12860,12932,13016,13092,13170,13246,13429,13497,13617"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,138,226", "endColumns": "82,87,87", "endOffsets": "133,221,309"}, "to": {"startLines": "23,158,159", "startColumns": "4,4,4", "startOffsets": "994,13622,13710", "endColumns": "82,87,87", "endOffsets": "1072,13705,13793"}}]}]}