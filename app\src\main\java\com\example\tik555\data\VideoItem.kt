package com.example.tik555.data

import com.google.gson.annotations.SerializedName

/**
 * 视频数据模型
 */
data class VideoItem(
    @SerializedName("url")
    val url: String,
    
    @SerializedName("video_id")
    val videoId: String,
    
    @SerializedName("Category")
    val category: Int,
    
    @SerializedName("width")
    val width: Int,
    
    @SerializedName("height")
    val height: Int
) {
    /**
     * 获取视频宽高比
     */
    val aspectRatio: Float
        get() = if (height > 0) width.toFloat() / height.toFloat() else 16f / 9f
}

/**
 * API响应数据模型
 */
data class VideoResponse(
    @SerializedName("code")
    val code: Int,
    
    @SerializedName("data")
    val data: VideoData
)

data class VideoData(
    @SerializedName("list")
    val list: List<VideoItem>
)
