{"logs": [{"outputFile": "com.example.tik555.app-mergeReleaseResources-2:/values-uk/values-uk.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\457147f8f67deaf2a3ec4b5417c706c3\\transformed\\media3-exoplayer-1.4.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,262,333,413,486,579,668", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "124,189,257,328,408,481,574,663,738"}, "to": {"startLines": "60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4396,4470,4535,4603,4674,4754,4827,4920,5009", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "4465,4530,4598,4669,4749,4822,4915,5004,5079"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,136,233", "endColumns": "80,96,99", "endOffsets": "131,228,328"}, "to": {"startLines": "23,158,159", "startColumns": "4,4,4", "startOffsets": "1006,13747,13844", "endColumns": "80,96,99", "endOffsets": "1082,13839,13939"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,407,525,624,719,831,969,1085,1232,1316,1416,1509,1605,1721,1845,1950,2091,2228,2363,2552,2679,2803,2932,3053,3147,3248,3374,3504,3602,3707,3816,3961,4112,4220,4320,4395,4490,4586,4705,4791,4878,4977,5057,5143,5242,5346,5441,5541,5630,5737,5833,5936,6054,6134,6249", "endColumns": "117,115,117,117,98,94,111,137,115,146,83,99,92,95,115,123,104,140,136,134,188,126,123,128,120,93,100,125,129,97,104,108,144,150,107,99,74,94,95,118,85,86,98,79,85,98,103,94,99,88,106,95,102,117,79,114,105", "endOffsets": "168,284,402,520,619,714,826,964,1080,1227,1311,1411,1504,1600,1716,1840,1945,2086,2223,2358,2547,2674,2798,2927,3048,3142,3243,3369,3499,3597,3702,3811,3956,4107,4215,4315,4390,4485,4581,4700,4786,4873,4972,5052,5138,5237,5341,5436,5536,5625,5732,5828,5931,6049,6129,6244,6350"}, "to": {"startLines": "88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6339,6457,6573,6691,6809,6908,7003,7115,7253,7369,7516,7600,7700,7793,7889,8005,8129,8234,8375,8512,8647,8836,8963,9087,9216,9337,9431,9532,9658,9788,9886,9991,10100,10245,10396,10504,10604,10679,10774,10870,10989,11075,11162,11261,11341,11427,11526,11630,11725,11825,11914,12021,12117,12220,12338,12418,12533", "endColumns": "117,115,117,117,98,94,111,137,115,146,83,99,92,95,115,123,104,140,136,134,188,126,123,128,120,93,100,125,129,97,104,108,144,150,107,99,74,94,95,118,85,86,98,79,85,98,103,94,99,88,106,95,102,117,79,114,105", "endOffsets": "6452,6568,6686,6804,6903,6998,7110,7248,7364,7511,7595,7695,7788,7884,8000,8124,8229,8370,8507,8642,8831,8958,9082,9211,9332,9426,9527,9653,9783,9881,9986,10095,10240,10391,10499,10599,10674,10769,10865,10984,11070,11157,11256,11336,11422,11521,11625,11720,11820,11909,12016,12112,12215,12333,12413,12528,12634"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\e874b47f582ea1f96bd82c21770ae83f\\transformed\\media3-ui-1.4.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,626,956,1040,1122,1205,1305,1404,1489,1552,1650,1749,1820,1889,1955,2023,2149,2274,2411,2488,2570,2645,2733,2828,2921,2989,3074,3127,3187,3235,3296,3363,3431,3495,3562,3627,3687,3753,3805,3866,3951,4036,4091", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,83,81,82,99,98,84,62,97,98,70,68,65,67,125,124,136,76,81,74,87,94,92,67,84,52,59,47,60,66,67,63,66,64,59,65,51,60,84,84,54,66", "endOffsets": "282,621,951,1035,1117,1200,1300,1399,1484,1547,1645,1744,1815,1884,1950,2018,2144,2269,2406,2483,2565,2640,2728,2823,2916,2984,3069,3122,3182,3230,3291,3358,3426,3490,3557,3622,3682,3748,3800,3861,3946,4031,4086,4153"}, "to": {"startLines": "2,11,17,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,676,2278,2362,2444,2527,2627,2726,2811,2874,2972,3071,3142,3211,3277,3345,3471,3596,3733,3810,3892,3967,4055,4150,4243,4311,5084,5137,5197,5245,5306,5373,5441,5505,5572,5637,5697,5763,5815,5876,5961,6046,6101", "endLines": "10,16,22,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "17,12,12,83,81,82,99,98,84,62,97,98,70,68,65,67,125,124,136,76,81,74,87,94,92,67,84,52,59,47,60,66,67,63,66,64,59,65,51,60,84,84,54,66", "endOffsets": "332,671,1001,2357,2439,2522,2622,2721,2806,2869,2967,3066,3137,3206,3272,3340,3466,3591,3728,3805,3887,3962,4050,4145,4238,4306,4391,5132,5192,5240,5301,5368,5436,5500,5567,5632,5692,5758,5810,5871,5956,6041,6096,6163"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "24,25,26,27,28,29,30,154", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1087,1187,1289,1390,1491,1596,1701,13373", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "1182,1284,1385,1486,1591,1696,1809,13469"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "187,280,364,466,567,651,733,822,910,992,1077,1165,1237,1326,1402,1479,1556,1636,1706", "endColumns": "92,83,101,100,83,81,88,87,81,84,87,71,88,75,76,76,79,69,122", "endOffsets": "275,359,461,562,646,728,817,905,987,1072,1160,1232,1321,1397,1474,1551,1631,1701,1824"}, "to": {"startLines": "31,32,33,34,35,86,87,145,146,147,148,149,150,151,152,153,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1814,1907,1991,2093,2194,6168,6250,12639,12727,12809,12894,12982,13054,13143,13219,13296,13474,13554,13624", "endColumns": "92,83,101,100,83,81,88,87,81,84,87,71,88,75,76,76,79,69,122", "endOffsets": "1902,1986,2088,2189,2273,6245,6334,12722,12804,12889,12977,13049,13138,13214,13291,13368,13549,13619,13742"}}]}]}