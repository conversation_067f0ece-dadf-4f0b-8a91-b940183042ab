{"logs": [{"outputFile": "com.example.tik555.app-mergeDebugResources-2:/values-bn/values-bn.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cb4e11135627ad7155a925c218efe46e\\transformed\\media3-ui-1.4.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,497,691,776,860,941,1034,1130,1206,1272,1361,1450,1517,1581,1643,1716,1832,1948,2066,2137,2220,2289,2365,2453,2540,2604,2669,2722,2784,2832,2893,2953,3015,3079,3145,3202,3266,3331,3384,3447,3524,3601,3653", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,84,83,80,92,95,75,65,88,88,66,63,61,72,115,115,117,70,82,68,75,87,86,63,64,52,61,47,60,59,61,63,65,56,63,64,52,62,76,76,51,63", "endOffsets": "283,492,686,771,855,936,1029,1125,1201,1267,1356,1445,1512,1576,1638,1711,1827,1943,2061,2132,2215,2284,2360,2448,2535,2599,2664,2717,2779,2827,2888,2948,3010,3074,3140,3197,3261,3326,3379,3442,3519,3596,3648,3712"}, "to": {"startLines": "2,11,15,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,338,547,1995,2080,2164,2245,2338,2434,2510,2576,2665,2754,2821,2885,2947,3020,3136,3252,3370,3441,3524,3593,3669,3757,3844,3908,4732,4785,4847,4895,4956,5016,5078,5142,5208,5265,5329,5394,5447,5510,5587,5664,5716", "endLines": "10,14,18,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,84,83,80,92,95,75,65,88,88,66,63,61,72,115,115,117,70,82,68,75,87,86,63,64,52,61,47,60,59,61,63,65,56,63,64,52,62,76,76,51,63", "endOffsets": "333,542,736,2075,2159,2240,2333,2429,2505,2571,2660,2749,2816,2880,2942,3015,3131,3247,3365,3436,3519,3588,3664,3752,3839,3903,3968,4780,4842,4890,4951,5011,5073,5137,5203,5260,5324,5389,5442,5505,5582,5659,5711,5775"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\402c9dcc315b2462c9ffa2b8bd9a3c9c\\transformed\\ui-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,273,357,447,545,631,710,816,903,992,1070,1151,1234,1320,1396,1473,1549,1624,1692", "endColumns": "93,83,89,97,85,78,105,86,88,77,80,82,85,75,76,75,74,67,117", "endOffsets": "268,352,442,540,626,705,811,898,987,1065,1146,1229,1315,1391,1468,1544,1619,1687,1805"}, "to": {"startLines": "27,28,29,30,31,82,83,142,143,144,145,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1543,1637,1721,1811,1909,5780,5859,12415,12502,12591,12669,12750,12833,12919,12995,13072,13249,13324,13392", "endColumns": "93,83,89,97,85,78,105,86,88,77,80,82,85,75,76,75,74,67,117", "endOffsets": "1632,1716,1806,1904,1990,5854,5960,12497,12586,12664,12745,12828,12914,12990,13067,13143,13319,13387,13505"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\ff3f81a0c1699dcfcdb03be15b69c061\\transformed\\material-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "141", "startColumns": "4", "startOffsets": "12325", "endColumns": "89", "endOffsets": "12410"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0fd183eed73303e0d8adf1f47d0f3fe5\\transformed\\media3-exoplayer-1.4.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,256,322,397,464,596,725", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "118,184,251,317,392,459,591,720,809"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3973,4041,4107,4174,4240,4315,4382,4514,4643", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "4036,4102,4169,4235,4310,4377,4509,4638,4727"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\19d069744fa00860960c2518b4a58305\\transformed\\foundation-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,84", "endOffsets": "123,208,293"}, "to": {"startLines": "19,155,156", "startColumns": "4,4,4", "startOffsets": "741,13510,13595", "endColumns": "72,84,84", "endOffsets": "809,13590,13675"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\95d12178e9e01a63c029617100d83575\\transformed\\core-1.16.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "20,21,22,23,24,25,26,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "814,913,1015,1117,1220,1321,1423,13148", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "908,1010,1112,1215,1316,1418,1538,13244"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\82da4bd88c75535472e099c38aac915b\\transformed\\material3-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,193,330,449,583,700,799,915,1057,1178,1320,1405,1511,1605,1706,1835,1964,2075,2204,2331,2461,2641,2763,2883,3005,3136,3231,3326,3459,3606,3703,3808,3918,4045,4177,4284,4385,4462,4565,4665,4771,4862,4952,5055,5135,5220,5321,5425,5518,5623,5710,5816,5915,6023,6141,6221,6321", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,105,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "188,325,444,578,695,794,910,1052,1173,1315,1400,1506,1600,1701,1830,1959,2070,2199,2326,2456,2636,2758,2878,3000,3131,3226,3321,3454,3601,3698,3803,3913,4040,4172,4279,4380,4457,4560,4660,4766,4857,4947,5050,5130,5215,5316,5420,5513,5618,5705,5811,5910,6018,6136,6216,6316,6410"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5965,6103,6240,6359,6493,6610,6709,6825,6967,7088,7230,7315,7421,7515,7616,7745,7874,7985,8114,8241,8371,8551,8673,8793,8915,9046,9141,9236,9369,9516,9613,9718,9828,9955,10087,10194,10295,10372,10475,10575,10681,10772,10862,10965,11045,11130,11231,11335,11428,11533,11620,11726,11825,11933,12051,12131,12231", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,105,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "6098,6235,6354,6488,6605,6704,6820,6962,7083,7225,7310,7416,7510,7611,7740,7869,7980,8109,8236,8366,8546,8668,8788,8910,9041,9136,9231,9364,9511,9608,9713,9823,9950,10082,10189,10290,10367,10470,10570,10676,10767,10857,10960,11040,11125,11226,11330,11423,11528,11615,11721,11820,11928,12046,12126,12226,12320"}}]}]}