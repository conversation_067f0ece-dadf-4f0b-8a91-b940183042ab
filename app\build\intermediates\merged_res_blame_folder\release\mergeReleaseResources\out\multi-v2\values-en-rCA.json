{"logs": [{"outputFile": "com.example.tik555.app-mergeReleaseResources-2:/values-en-rCA/values-en-rCA.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,657,773", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "146,248,347,446,550,652,768,869"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "180,276,378,477,576,680,782,8335", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "271,373,472,571,675,777,893,8431"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,400,514,613,708,820,956,1072,1208,1292,1391,1482,1579,1698,1823,1927,2054,2177,2305,2467,2588,2704,2827,2952,3044,3142,3259,3383,3480,3582,3684,3814,3953,4059,4158,4234,4330,4424,4528,4615,4702,4804,4884,4968,5069,5170,5270,5369,5457,5563,5664,5768,5884,5964,6064", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,161,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,75,95,93,103,86,86,101,79,83,100,100,99,98,87,105,100,103,115,79,99,94", "endOffsets": "168,284,395,509,608,703,815,951,1067,1203,1287,1386,1477,1574,1693,1818,1922,2049,2172,2300,2462,2583,2699,2822,2947,3039,3137,3254,3378,3475,3577,3679,3809,3948,4054,4153,4229,4325,4419,4523,4610,4697,4799,4879,4963,5064,5165,5265,5364,5452,5558,5659,5763,5879,5959,6059,6154"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1523,1641,1757,1868,1982,2081,2176,2288,2424,2540,2676,2760,2859,2950,3047,3166,3291,3395,3522,3645,3773,3935,4056,4172,4295,4420,4512,4610,4727,4851,4948,5050,5152,5282,5421,5527,5626,5702,5798,5892,5996,6083,6170,6272,6352,6436,6537,6638,6738,6837,6925,7031,7132,7236,7352,7432,7532", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,161,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,75,95,93,103,86,86,101,79,83,100,100,99,98,87,105,100,103,115,79,99,94", "endOffsets": "1636,1752,1863,1977,2076,2171,2283,2419,2535,2671,2755,2854,2945,3042,3161,3286,3390,3517,3640,3768,3930,4051,4167,4290,4415,4507,4605,4722,4846,4943,5045,5147,5277,5416,5522,5621,5697,5793,5887,5991,6078,6165,6267,6347,6431,6532,6633,6733,6832,6920,7026,7127,7231,7347,7427,7527,7622"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,216", "endColumns": "74,85,84", "endOffsets": "125,211,296"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8699,8785", "endColumns": "74,85,84", "endOffsets": "175,8780,8865"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "181,273,355,449,548,634,716,806,895,979,1057,1139,1212,1296,1372,1444,1514,1591,1657", "endColumns": "91,81,93,98,85,81,89,88,83,77,81,72,83,75,71,69,76,65,119", "endOffsets": "268,350,444,543,629,711,801,890,974,1052,1134,1207,1291,1367,1439,1509,1586,1652,1772"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "898,990,1072,1166,1265,1351,1433,7627,7716,7800,7878,7960,8033,8117,8193,8265,8436,8513,8579", "endColumns": "91,81,93,98,85,81,89,88,83,77,81,72,83,75,71,69,76,65,119", "endOffsets": "985,1067,1161,1260,1346,1428,1518,7711,7795,7873,7955,8028,8112,8188,8260,8330,8508,8574,8694"}}]}]}