{"logs": [{"outputFile": "com.example.tik555.app-mergeDebugResources-2:/values-ur/values-ur.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\dc0228f35d5d9920c8fe531ae3e0365a\\transformed\\media3-exoplayer-1.7.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,194,266,336,413,484,575,660", "endColumns": "71,66,71,69,76,70,90,84,78", "endOffsets": "122,189,261,331,408,479,570,655,734"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3924,3996,4063,4135,4205,4282,4353,4444,4529", "endColumns": "71,66,71,69,76,70,90,84,78", "endOffsets": "3991,4058,4130,4200,4277,4348,4439,4524,4603"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,86", "endOffsets": "123,208,295"}, "to": {"startLines": "19,155,156", "startColumns": "4,4,4", "startOffsets": "725,13323,13408", "endColumns": "72,84,86", "endOffsets": "793,13403,13490"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,273,356,446,543,631,712,805,893,979,1062,1147,1222,1305,1383,1457,1530,1605,1671", "endColumns": "93,82,89,96,87,80,92,87,85,82,84,74,82,77,73,72,74,65,116", "endOffsets": "268,351,441,538,626,707,800,888,974,1057,1142,1217,1300,1378,1452,1525,1600,1666,1783"}, "to": {"startLines": "27,28,29,30,31,82,83,142,143,144,145,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1519,1613,1696,1786,1883,5640,5721,12239,12327,12413,12496,12581,12656,12739,12817,12891,13065,13140,13206", "endColumns": "93,82,89,96,87,80,92,87,85,82,84,74,82,77,73,72,74,65,116", "endOffsets": "1608,1691,1781,1878,1966,5716,5809,12322,12408,12491,12576,12651,12734,12812,12886,12959,13135,13201,13318"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\17d8797a605bf267136b984c4f4c6900\\transformed\\media3-ui-1.7.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,282,488,675,763,854,939,1034,1129,1197,1259,1348,1437,1507,1572,1634,1702,1812,1924,2033,2107,2188,2258,2326,2412,2501,2565,2628,2681,2739,2787,2848,2908,2977,3037,3100,3160,3223,3288,3341,3398,3469,3540,3594", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,87,90,84,94,94,67,61,88,88,69,64,61,67,109,111,108,73,80,69,67,85,88,63,62,52,57,47,60,59,68,59,62,59,62,64,52,56,70,70,53,65", "endOffsets": "277,483,670,758,849,934,1029,1124,1192,1254,1343,1432,1502,1567,1629,1697,1807,1919,2028,2102,2183,2253,2321,2407,2496,2560,2623,2676,2734,2782,2843,2903,2972,3032,3095,3155,3218,3283,3336,3393,3464,3535,3589,3655"}, "to": {"startLines": "2,11,15,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,332,538,1971,2059,2150,2235,2330,2425,2493,2555,2644,2733,2803,2868,2930,2998,3108,3220,3329,3403,3484,3554,3622,3708,3797,3861,4608,4661,4719,4767,4828,4888,4957,5017,5080,5140,5203,5268,5321,5378,5449,5520,5574", "endLines": "10,14,18,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,87,90,84,94,94,67,61,88,88,69,64,61,67,109,111,108,73,80,69,67,85,88,63,62,52,57,47,60,59,68,59,62,59,62,64,52,56,70,70,53,65", "endOffsets": "327,533,720,2054,2145,2230,2325,2420,2488,2550,2639,2728,2798,2863,2925,2993,3103,3215,3324,3398,3479,3549,3617,3703,3792,3856,3919,4656,4714,4762,4823,4883,4952,5012,5075,5135,5198,5263,5316,5373,5444,5515,5569,5635"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "20,21,22,23,24,25,26,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "798,896,998,1100,1204,1307,1405,12964", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "891,993,1095,1199,1302,1400,1514,13060"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\8a2e8e7142c38a8f2ee7133e4de5b14c\\transformed\\material-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "86", "endOffsets": "137"}, "to": {"startLines": "141", "startColumns": "4", "startOffsets": "12152", "endColumns": "86", "endOffsets": "12234"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,295,411,529,627,724,839,974,1098,1238,1323,1427,1523,1623,1740,1870,1979,2123,2266,2395,2593,2718,2837,2960,3098,3195,3290,3414,3538,3639,3744,3850,3993,4142,4248,4352,4428,4524,4621,4733,4823,4914,5029,5109,5194,5297,5403,5500,5603,5688,5794,5893,5996,6117,6197,6299", "endColumns": "117,121,115,117,97,96,114,134,123,139,84,103,95,99,116,129,108,143,142,128,197,124,118,122,137,96,94,123,123,100,104,105,142,148,105,103,75,95,96,111,89,90,114,79,84,102,105,96,102,84,105,98,102,120,79,101,93", "endOffsets": "168,290,406,524,622,719,834,969,1093,1233,1318,1422,1518,1618,1735,1865,1974,2118,2261,2390,2588,2713,2832,2955,3093,3190,3285,3409,3533,3634,3739,3845,3988,4137,4243,4347,4423,4519,4616,4728,4818,4909,5024,5104,5189,5292,5398,5495,5598,5683,5789,5888,5991,6112,6192,6294,6388"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5814,5932,6054,6170,6288,6386,6483,6598,6733,6857,6997,7082,7186,7282,7382,7499,7629,7738,7882,8025,8154,8352,8477,8596,8719,8857,8954,9049,9173,9297,9398,9503,9609,9752,9901,10007,10111,10187,10283,10380,10492,10582,10673,10788,10868,10953,11056,11162,11259,11362,11447,11553,11652,11755,11876,11956,12058", "endColumns": "117,121,115,117,97,96,114,134,123,139,84,103,95,99,116,129,108,143,142,128,197,124,118,122,137,96,94,123,123,100,104,105,142,148,105,103,75,95,96,111,89,90,114,79,84,102,105,96,102,84,105,98,102,120,79,101,93", "endOffsets": "5927,6049,6165,6283,6381,6478,6593,6728,6852,6992,7077,7181,7277,7377,7494,7624,7733,7877,8020,8149,8347,8472,8591,8714,8852,8949,9044,9168,9292,9393,9498,9604,9747,9896,10002,10106,10182,10278,10375,10487,10577,10668,10783,10863,10948,11051,11157,11254,11357,11442,11548,11647,11750,11871,11951,12053,12147"}}]}]}