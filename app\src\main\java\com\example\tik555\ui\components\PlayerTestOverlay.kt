package com.example.tik555.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.tik555.data.VideoItem
import com.example.tik555.player.PlayerManager

/**
 * 播放器测试覆盖层
 * 用于测试播放器创建、销毁和缓存功能
 */
@Composable
fun PlayerTestOverlay(
    currentVideoIndex: Int,
    currentVideoItem: VideoItem?,
    playerManager: PlayerManager,
    videoList: List<VideoItem>,
    modifier: Modifier = Modifier
) {
    var showTestInfo by remember { mutableStateOf(false) }
    
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.TopEnd
    ) {
        // 测试信息面板
        if (showTestInfo) {
            PlayerTestPanel(
                currentVideoIndex = currentVideoIndex,
                currentVideoItem = currentVideoItem,
                playerManager = playerManager,
                videoList = videoList,
                modifier = Modifier
                    .padding(16.dp)
                    .background(
                        Color.Blue.copy(alpha = 0.8f),
                        shape = MaterialTheme.shapes.medium
                    )
                    .padding(12.dp)
            )
        }
        
        // 测试按钮
        Box(
            modifier = Modifier
                .padding(16.dp)
                .size(56.dp)
                .clip(CircleShape)
                .background(Color.Blue.copy(alpha = 0.7f))
                .clickable { showTestInfo = !showTestInfo },
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "TEST",
                color = Color.White,
                fontSize = 10.sp,
                fontFamily = FontFamily.Monospace
            )
        }
    }
}

@Composable
private fun PlayerTestPanel(
    currentVideoIndex: Int,
    currentVideoItem: VideoItem?,
    playerManager: PlayerManager,
    videoList: List<VideoItem>,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "=== 播放器测试 ===",
            color = Color.White,
            fontSize = 14.sp,
            fontFamily = FontFamily.Monospace
        )
        
        // 当前状态
        Text(
            text = "当前视频: $currentVideoIndex",
            color = Color.White,
            fontSize = 12.sp,
            fontFamily = FontFamily.Monospace
        )
        
        val playerInfo = playerManager.getPlayerInfo()
        Text(
            text = "播放器总数: ${playerInfo["totalPlayers"]}",
            color = Color.White,
            fontSize = 12.sp,
            fontFamily = FontFamily.Monospace
        )
        
        Text(
            text = "活跃播放器: ${playerInfo["activePlayers"]}",
            color = Color.White,
            fontSize = 12.sp,
            fontFamily = FontFamily.Monospace
        )
        
        Text(
            text = "缓存大小: ${formatCacheSize(playerInfo["cacheSize"] as? Long ?: 0L)}",
            color = Color.White,
            fontSize = 12.sp,
            fontFamily = FontFamily.Monospace
        )
        
        // 测试按钮
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 预加载测试
            Box(
                modifier = Modifier
                    .background(Color.Green.copy(alpha = 0.7f))
                    .clickable {
                        // 测试预加载
                        playerManager.preloadVideos(currentVideoIndex, videoList)
                    }
                    .padding(8.dp)
            ) {
                Text(
                    text = "预加载",
                    color = Color.White,
                    fontSize = 10.sp
                )
            }
            
            // 强制销毁测试
            Box(
                modifier = Modifier
                    .background(Color.Red.copy(alpha = 0.7f))
                    .clickable {
                        // 测试播放器销毁 - 模拟跳转到远距离视频
                        val farIndex = if (currentVideoIndex < videoList.size - 5) {
                            currentVideoIndex + 5
                        } else {
                            maxOf(0, currentVideoIndex - 5)
                        }
                        if (farIndex < videoList.size) {
                            playerManager.getPlayerForVideo(farIndex, videoList[farIndex])
                        }
                    }
                    .padding(8.dp)
            ) {
                Text(
                    text = "测试销毁",
                    color = Color.White,
                    fontSize = 10.sp
                )
            }
        }
        
        // 播放器状态详情
        (playerInfo["playerStates"] as? List<*>)?.forEach { state ->
            if (state is Map<*, *>) {
                val videoIndex = state["videoIndex"] as? Int ?: -1
                val isPlaying = state["isPlaying"] as? Boolean ?: false
                val playbackState = state["playbackState"] as? Int ?: 0
                
                Text(
                    text = "播放器[$videoIndex]: ${getPlaybackStateText(playbackState)} ${if (isPlaying) "▶️" else "⏸️"}",
                    color = if (videoIndex == currentVideoIndex) Color.Yellow else Color.White,
                    fontSize = 11.sp,
                    fontFamily = FontFamily.Monospace
                )
            }
        }
    }
}

private fun formatCacheSize(bytes: Long): String {
    val mb = bytes / (1024 * 1024)
    return "${mb}MB"
}

private fun getPlaybackStateText(state: Int): String {
    return when (state) {
        1 -> "空闲"
        2 -> "缓冲"
        3 -> "就绪"
        4 -> "结束"
        else -> "未知($state)"
    }
}
