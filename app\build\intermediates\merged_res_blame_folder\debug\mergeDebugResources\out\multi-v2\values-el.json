{"logs": [{"outputFile": "com.example.tik555.app-mergeDebugResources-2:/values-el/values-el.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,303,417,539,643,746,866,1017,1145,1303,1393,1493,1592,1697,1815,1941,2046,2188,2324,2468,2648,2786,2906,3033,3157,3257,3356,3492,3629,3735,3841,3951,4095,4248,4362,4468,4555,4653,4750,4863,4953,5042,5145,5225,5308,5407,5509,5606,5704,5791,5897,5996,6098,6219,6299,6415", "endColumns": "123,123,113,121,103,102,119,150,127,157,89,99,98,104,117,125,104,141,135,143,179,137,119,126,123,99,98,135,136,105,105,109,143,152,113,105,86,97,96,112,89,88,102,79,82,98,101,96,97,86,105,98,101,120,79,115,106", "endOffsets": "174,298,412,534,638,741,861,1012,1140,1298,1388,1488,1587,1692,1810,1936,2041,2183,2319,2463,2643,2781,2901,3028,3152,3252,3351,3487,3624,3730,3836,3946,4090,4243,4357,4463,4550,4648,4745,4858,4948,5037,5140,5220,5303,5402,5504,5601,5699,5786,5892,5991,6093,6214,6294,6410,6517"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6119,6243,6367,6481,6603,6707,6810,6930,7081,7209,7367,7457,7557,7656,7761,7879,8005,8110,8252,8388,8532,8712,8850,8970,9097,9221,9321,9420,9556,9693,9799,9905,10015,10159,10312,10426,10532,10619,10717,10814,10927,11017,11106,11209,11289,11372,11471,11573,11670,11768,11855,11961,12060,12162,12283,12363,12479", "endColumns": "123,123,113,121,103,102,119,150,127,157,89,99,98,104,117,125,104,141,135,143,179,137,119,126,123,99,98,135,136,105,105,109,143,152,113,105,86,97,96,112,89,88,102,79,82,98,101,96,97,86,105,98,101,120,79,115,106", "endOffsets": "6238,6362,6476,6598,6702,6805,6925,7076,7204,7362,7452,7552,7651,7756,7874,8000,8105,8247,8383,8527,8707,8845,8965,9092,9216,9316,9415,9551,9688,9794,9900,10010,10154,10307,10421,10527,10614,10712,10809,10922,11012,11101,11204,11284,11367,11466,11568,11665,11763,11850,11956,12055,12157,12278,12358,12474,12581"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values-el\\values-el.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "192,291,377,478,583,675,756,850,939,1029,1110,1192,1267,1356,1431,1509,1584,1663,1733", "endColumns": "98,85,100,104,91,80,93,88,89,80,81,74,88,74,77,74,78,69,122", "endOffsets": "286,372,473,578,670,751,845,934,1024,1105,1187,1262,1351,1426,1504,1579,1658,1728,1851"}, "to": {"startLines": "27,28,29,30,31,82,83,142,143,144,145,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1609,1708,1794,1895,2000,5944,6025,12679,12768,12858,12939,13021,13096,13185,13260,13338,13514,13593,13663", "endColumns": "98,85,100,104,91,80,93,88,89,80,81,74,88,74,77,74,78,69,122", "endOffsets": "1703,1789,1890,1995,2087,6020,6114,12763,12853,12934,13016,13091,13180,13255,13333,13408,13588,13658,13781"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,239", "endColumns": "85,97,101", "endOffsets": "136,234,336"}, "to": {"startLines": "19,155,156", "startColumns": "4,4,4", "startOffsets": "788,13786,13884", "endColumns": "85,97,101", "endOffsets": "869,13879,13981"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "20,21,22,23,24,25,26,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "874,972,1075,1175,1278,1386,1492,13413", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "967,1070,1170,1273,1381,1487,1604,13509"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\dc0228f35d5d9920c8fe531ae3e0365a\\transformed\\media3-exoplayer-1.7.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,184,242,305,379,455,554,649", "endColumns": "70,57,57,62,73,75,98,94,66", "endOffsets": "121,179,237,300,374,450,549,644,711"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4195,4266,4324,4382,4445,4519,4595,4694,4789", "endColumns": "70,57,57,62,73,75,98,94,66", "endOffsets": "4261,4319,4377,4440,4514,4590,4689,4784,4851"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\17d8797a605bf267136b984c4f4c6900\\transformed\\media3-ui-1.7.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,510,738,826,915,997,1080,1172,1269,1335,1431,1527,1592,1662,1727,1801,1923,2046,2169,2239,2322,2394,2491,2596,2700,2766,2841,2894,2952,3006,3067,3132,3201,3266,3338,3400,3460,3525,3583,3649,3729,3809,3863", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,87,88,81,82,91,96,65,95,95,64,69,64,73,121,122,122,69,82,71,96,104,103,65,74,52,57,53,60,64,68,64,71,61,59,64,57,65,79,79,53,65", "endOffsets": "282,505,733,821,910,992,1075,1167,1264,1330,1426,1522,1587,1657,1722,1796,1918,2041,2164,2234,2317,2389,2486,2591,2695,2761,2836,2889,2947,3001,3062,3127,3196,3261,3333,3395,3455,3520,3578,3644,3724,3804,3858,3924"}, "to": {"startLines": "2,11,15,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,560,2092,2180,2269,2351,2434,2526,2623,2689,2785,2881,2946,3016,3081,3155,3277,3400,3523,3593,3676,3748,3845,3950,4054,4120,4856,4909,4967,5021,5082,5147,5216,5281,5353,5415,5475,5540,5598,5664,5744,5824,5878", "endLines": "10,14,18,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,87,88,81,82,91,96,65,95,95,64,69,64,73,121,122,122,69,82,71,96,104,103,65,74,52,57,53,60,64,68,64,71,61,59,64,57,65,79,79,53,65", "endOffsets": "332,555,783,2175,2264,2346,2429,2521,2618,2684,2780,2876,2941,3011,3076,3150,3272,3395,3518,3588,3671,3743,3840,3945,4049,4115,4190,4904,4962,5016,5077,5142,5211,5276,5348,5410,5470,5535,5593,5659,5739,5819,5873,5939"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\8a2e8e7142c38a8f2ee7133e4de5b14c\\transformed\\material-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "141", "startColumns": "4", "startOffsets": "12586", "endColumns": "92", "endOffsets": "12674"}}]}]}