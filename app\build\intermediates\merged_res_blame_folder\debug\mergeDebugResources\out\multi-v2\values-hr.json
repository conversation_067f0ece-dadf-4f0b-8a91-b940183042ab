{"logs": [{"outputFile": "com.example.tik555.app-mergeDebugResources-2:/values-hr/values-hr.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\17d8797a605bf267136b984c4f4c6900\\transformed\\media3-ui-1.7.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,295,585,864,945,1027,1107,1214,1321,1391,1458,1549,1641,1706,1777,1840,1912,2031,2155,2276,2344,2428,2499,2570,2674,2779,2846,2911,2964,3022,3070,3131,3205,3284,3360,3434,3498,3557,3628,3680,3743,3828,3913,3969", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,118,123,120,67,83,70,70,103,104,66,64,52,57,47,60,73,78,75,73,63,58,70,51,62,84,84,55,67", "endOffsets": "290,580,859,940,1022,1102,1209,1316,1386,1453,1544,1636,1701,1772,1835,1907,2026,2150,2271,2339,2423,2494,2565,2669,2774,2841,2906,2959,3017,3065,3126,3200,3279,3355,3429,3493,3552,3623,3675,3738,3823,3908,3964,4032"}, "to": {"startLines": "2,11,16,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,345,635,2205,2286,2368,2448,2555,2662,2732,2799,2890,2982,3047,3118,3181,3253,3372,3496,3617,3685,3769,3840,3911,4015,4120,4187,4918,4971,5029,5077,5138,5212,5291,5367,5441,5505,5564,5635,5687,5750,5835,5920,5976", "endLines": "10,15,20,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,118,123,120,67,83,70,70,103,104,66,64,52,57,47,60,73,78,75,73,63,58,70,51,62,84,84,55,67", "endOffsets": "340,630,909,2281,2363,2443,2550,2657,2727,2794,2885,2977,3042,3113,3176,3248,3367,3491,3612,3680,3764,3835,3906,4010,4115,4182,4247,4966,5024,5072,5133,5207,5286,5362,5436,5500,5559,5630,5682,5745,5830,5915,5971,6039"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\8a2e8e7142c38a8f2ee7133e4de5b14c\\transformed\\material-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "143", "startColumns": "4", "startOffsets": "12523", "endColumns": "92", "endOffsets": "12611"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\c6763e4a76f69a31c0c315f3b843ead4\\transformed\\core-1.16.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "22,23,24,25,26,27,28,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1004,1102,1209,1306,1405,1509,1613,13358", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "1097,1204,1301,1400,1504,1608,1725,13454"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\f1146f774d6258b00f26aa6ad8af6b00\\transformed\\ui-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,301,388,482,581,671,750,843,938,1023,1104,1190,1263,1352,1429,1508,1585,1664,1734", "endColumns": "104,86,93,98,89,78,92,94,84,80,85,72,88,76,78,76,78,69,117", "endOffsets": "296,383,477,576,666,745,838,933,1018,1099,1185,1258,1347,1424,1503,1580,1659,1729,1847"}, "to": {"startLines": "29,30,31,32,33,84,85,144,145,146,147,148,149,150,151,152,154,155,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1730,1835,1922,2016,2115,6044,6123,12616,12711,12796,12877,12963,13036,13125,13202,13281,13459,13538,13608", "endColumns": "104,86,93,98,89,78,92,94,84,80,85,72,88,76,78,76,78,69,117", "endOffsets": "1830,1917,2011,2110,2200,6118,6211,12706,12791,12872,12958,13031,13120,13197,13276,13353,13533,13603,13721"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\dc0228f35d5d9920c8fe531ae3e0365a\\transformed\\media3-exoplayer-1.7.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,566,648", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "125,186,251,324,403,476,561,643,716"}, "to": {"startLines": "58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4252,4327,4388,4453,4526,4605,4678,4763,4845", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "4322,4383,4448,4521,4600,4673,4758,4840,4913"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\5b4ba3bb7e234dc8495fb2bbdf42cb63\\transformed\\foundation-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,228", "endColumns": "89,82,84", "endOffsets": "140,223,308"}, "to": {"startLines": "21,157,158", "startColumns": "4,4,4", "startOffsets": "914,13726,13809", "endColumns": "89,82,84", "endOffsets": "999,13804,13889"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\cfbe6762a9ebca8f43d22b008e11bcd0\\transformed\\material3-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,296,416,537,637,731,842,983,1102,1247,1332,1432,1527,1625,1744,1870,1975,2111,2246,2380,2548,2674,2798,2926,3050,3146,3244,3374,3508,3605,3707,3816,3957,4104,4213,4313,4398,4491,4586,4699,4793,4879,4988,5076,5159,5256,5357,5450,5547,5635,5743,5840,5942,6080,6170,6270", "endColumns": "119,120,119,120,99,93,110,140,118,144,84,99,94,97,118,125,104,135,134,133,167,125,123,127,123,95,97,129,133,96,101,108,140,146,108,99,84,92,94,112,93,85,108,87,82,96,100,92,96,87,107,96,101,137,89,99,91", "endOffsets": "170,291,411,532,632,726,837,978,1097,1242,1327,1427,1522,1620,1739,1865,1970,2106,2241,2375,2543,2669,2793,2921,3045,3141,3239,3369,3503,3600,3702,3811,3952,4099,4208,4308,4393,4486,4581,4694,4788,4874,4983,5071,5154,5251,5352,5445,5542,5630,5738,5835,5937,6075,6165,6265,6357"}, "to": {"startLines": "86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6216,6336,6457,6577,6698,6798,6892,7003,7144,7263,7408,7493,7593,7688,7786,7905,8031,8136,8272,8407,8541,8709,8835,8959,9087,9211,9307,9405,9535,9669,9766,9868,9977,10118,10265,10374,10474,10559,10652,10747,10860,10954,11040,11149,11237,11320,11417,11518,11611,11708,11796,11904,12001,12103,12241,12331,12431", "endColumns": "119,120,119,120,99,93,110,140,118,144,84,99,94,97,118,125,104,135,134,133,167,125,123,127,123,95,97,129,133,96,101,108,140,146,108,99,84,92,94,112,93,85,108,87,82,96,100,92,96,87,107,96,101,137,89,99,91", "endOffsets": "6331,6452,6572,6693,6793,6887,6998,7139,7258,7403,7488,7588,7683,7781,7900,8026,8131,8267,8402,8536,8704,8830,8954,9082,9206,9302,9400,9530,9664,9761,9863,9972,10113,10260,10369,10469,10554,10647,10742,10855,10949,11035,11144,11232,11315,11412,11513,11606,11703,11791,11899,11996,12098,12236,12326,12426,12518"}}]}]}